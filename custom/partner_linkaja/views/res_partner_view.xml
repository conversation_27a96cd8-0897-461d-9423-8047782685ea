<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <record id="account.res_partner_action_supplier" model="ir.actions.act_window">
        <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_vendor': True, 'vendor_view': True}</field>
        <field name="domain">[('is_vendor', '=', True)]</field>
    </record>

    <record id="res_partner_action_supplier_master" model="ir.actions.act_window">
        <field name="name">Supplier Master</field>
        <field name="res_model">res.partner</field>
        <field name="path">supplier</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{'search_default_supplier': 1,'res_partner_search_mode': 'supplier', 'default_is_company': True, 'default_supplier_rank': 1, 'default_is_vendor': True, 'vendor_view': True}</field>
        <field name="domain">[('is_vendor', '=', True)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Create a new supplier in your address book
            </p><p>
            Odoo helps you easily track all activities related to a supplier.
            </p>
        </field>
    </record>     

    <record id="res_partner_view_search_vendor" model="ir.ui.view">
        <field name="name">res.partner.search.inherit.vendor</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.res_partner_view_search"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='supplier']" position="attributes">
                <attribute name="domain">[('is_vendor', '=', True)]</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_partner_form_inherit" model="ir.ui.view">
        <field name="name">res.partner.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@id='company']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='child_ids']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <!-- <xpath expr="//field[@name='child_ids']" position="attributes">
                <attribute name="required">context.get('vendor_view')</attribute>
            </xpath> -->

            <xpath expr="//div[@name='button_box']" position="inside">
                <div class="oe_button_box" name="button_box">
                    <button name="action_open_procurement_updates" type="object"
                            class="oe_stat_button" icon="fa-list-alt"
                            invisible="not context.get('vendor_view')">
                        <div class="o_form_field o_stat_info">
                            <span class="o_stat_value">
                                <!-- <field name="procurement_count" widget="statinfo"/> -->
                            </span>
                            <span class="o_stat_text">Updates History</span>
                        </div>
                    </button>
                </div>
            </xpath>

            <!-- same customer_ext_linkaja -->
            <xpath expr="//header" position="inside">
                <button string="Submit" name="action_submit" type="object" class="oe_highlight" invisible="not context.get('vendor_view') or (context.get('vendor_view') and vendor_state != 'draft')" />
                <button string="Prospective" name="action_prospective" type="object" class="oe_highlight" invisible="not context.get('vendor_view') or (context.get('vendor_view') and vendor_state != 'submit')"/>
                <button string="Set to Draft" name="action_supplier_draft" type="object" class="oe_highlight" invisible="not context.get('vendor_view') or (context.get('vendor_view') and vendor_state not in ['submit', 'prospective'])"/>
                <button string="Submit" name="action_spendauthorized" type="object" class="oe_highlight" invisible="not context.get('vendor_view') or (context.get('vendor_view') and vendor_state != 'prospective') or (context.get('vendor_view') and not promote_spend_authorized)"/>
                <button string="Approve" name="approve" type="object" class="oe_highlight" invisible="not context.get('vendor_view') or (context.get('vendor_view') and vendor_state != 'duediligence')"/>
                <button string="Update Data" name="action_open_procurement_form" type="object" class="btn-info" invisible="not context.get('vendor_view') or (context.get('vendor_view') and vendor_state not in ['spendauthorized', 'prospective'])"/>
                <field name="vendor_state" force_save='1' invisible="not context.get('vendor_view')" widget="statusbar" />
            </xpath>

 
            <xpath expr="//field[@name='company_type']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>
            <xpath expr="//field[@name='company_type']" position="before">
                <div class="o_row">
                    <label for="is_vendor" string="Vendor"/>
                    <field name="is_vendor" string="Is Vendor?"/>
                </div>
            </xpath>
            <xpath expr="//button[@name='create_company']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>
            <xpath expr="//field[@name='company_name']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>
            <xpath expr="//field[@name='parent_id']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>
            <xpath expr="//div[@class='oe_title mb24']" position="before">
                <div class="mb-3">
                    <button name="action_open_self_service_popup" type="object" class="btn btn-primary"
                        string="Self-Service"
                        invisible="not context.get('vendor_view') or (context.get('vendor_view') and partner_type != 'procurement') or (context.get('vendor_view') and vendor_state != 'draft')"/>
                </div>
            </xpath>

             <xpath expr="//group[group[span[@name='address_name']]]" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>


            <xpath expr="//group[group[span[@name='address_name']]]" position="after">
                <group invisible="not context.get('vendor_view')">
                    <group>
                        <span class="o_form_label o_td_label o_address_type" name="address_name_vendor">
                            <field name="type" invisible="is_company" readonly="user_ids" required="not is_company" class="fw-bold"/>
                            <span invisible="not is_company">Address</span>
                        </span>
                        <div class="o_address_format">
                            <field name="street" placeholder="Street..." class="o_address_street"
                                readonly="(type == 'contact' and parent_id) or (context.get('vendor_view') and vendor_state != 'draft')"/>
                            <field name="street2" placeholder="Street 2..." class="o_address_street"
                                readonly="(type == 'contact' and parent_id) or (context.get('vendor_view') and vendor_state != 'draft')"/>
                            <field name="city" placeholder="City" class="o_address_city"
                                readonly="(type == 'contact' and parent_id) or (context.get('vendor_view') and vendor_state != 'draft')"/>
                            <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}"
                                readonly="(type == 'contact' and parent_id) or (context.get('vendor_view') and vendor_state != 'draft')" context="{'country_id': country_id, 'default_country_id': country_id, 'zip': zip}"/>
                            <field name="zip" placeholder="ZIP" class="o_address_zip"
                                readonly="(type == 'contact' and parent_id) or context.get('vendor_view') and vendor_state != 'draft'"/>
                        </div>
                        <field name="country_id" string="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'
                                    readonly="(type == 'contact' and parent_id) or (context.get('vendor_view') and vendor_state != 'draft')"/>
                        <field name="phone" readonly="context.get('vendor_view') and vendor_state != 'draft'" widget="phone"/>
                        <field name="mobile" widget="phone" readonly="context.get('vendor_view') and vendor_state != 'draft'" />
                        <field name="email" readonly="context.get('vendor_view') and vendor_state != 'draft'" widget="email" context="{'gravatar_image': True}" required="user_ids"/>
                        <field name="website" readonly="context.get('vendor_view') and vendor_state != 'draft'" string="Website" widget="url"/>
                        <field name="property_stock_supplier" readonly="context.get('vendor_view') and vendor_state != 'draft'" />
                        <separator/>
                    </group>
                    <group>
                        <field name="is_vendor" invisible="1" />
                        <field name="busines_category"  />
                        <field name="partner_type" readonly="context.get('vendor_view') and vendor_state != 'draft'" required="context.get('vendor_view')" />
                        <field name="supplier_type"  readonly="context.get('vendor_view') and vendor_state != 'draft'" invisible='1' />
                         <field name="supplier_type_id"  readonly="context.get('vendor_view') and vendor_state != 'draft'" required="context.get('vendor_view')" />
                        <field name="tax_document"  readonly="context.get('vendor_view') and vendor_state != 'draft'" required="context.get('vendor_view')" />
                        <field name="tax_organization_type" readonly="context.get('vendor_view') and vendor_state != 'draft'" required="context.get('vendor_view')"/>
                        <field name="npwp_number" readonly="context.get('vendor_view') and vendor_state != 'draft'" invisible="tax_document != 'npwp'"/>
                        <field name="ktp_number" readonly="context.get('vendor_view') and vendor_state != 'draft'" invisible="tax_document != 'ktp'"/>
                        <field name="cor_number" readonly="context.get('vendor_view') and vendor_state != 'draft'" invisible="tax_document != 'others'"/>
                        <field name="payment_term_id" readonly="context.get('vendor_view') and vendor_state != 'draft'" />
                        <field name="created_by" readonly="context.get('vendor_view') and vendor_state != 'draft'" />
                        <field name="vendor_state" force_save='1' string="Supplier Registration Type" readonly="1"/>
                        <field name="promote_spend_authorized" force_save='1' readonly="vendor_state != 'prospective'"/>
                        <field name="tags" widget="many2many_tags" readonly="context.get('vendor_view') and vendor_state != 'draft'"/>
                    </group>
                </group>
            </xpath>

             <!-- menu contract -->
            <!-- <xpath expr="//page[@name='contact_addresses']" position="attributes">
                <attribute name="invisible">is_vendor == True</attribute>
            </xpath> -->
            <!-- <xpath expr="//page[@name='accounting']" position="after">
                <page string="Contact" name="contact_supplier_page">
                    <field name="contact_supplier_ids" mode="kanban" editable="bottom">
                        <kanban>
                            <templates>
                                <t t-name="kanban-box">
                                    <div class="oe_kanban_global_click o_kanban_card">
                                        <div class="o_kanban_details">
                                            <strong><field name="contact_name"/></strong>
                                            <div><field name="contact_email" widget="email"/></div>
                                            <div><field name="contact_number" widget="phone"/></div>
                                        </div>
                                    </div>
                                </t>
                            </templates>
                        </kanban>
                        <form string="Contact Supplier">
                            <sheet>
                                <group>
                                    <field name="contact_name" required="1"/>
                                    <field name="contact_email"/>
                                    <field name="contact_number"/>
                                </group>
                            </sheet>
                        </form>
                    </field>
                </page>
                <page string="Accounting">
                    <group string="Bank Accounts" name="banks" >
                        <field name="bank_supplier_ids" nolabel="1"  widget="one2many_list">
                            <list>
                                <field name="acc_number"/>
                                <field name="bank_id"/>
                                <field name="allow_out_payment" string="Send Money"/>
                                <field name="acc_holder_name" column_invisible="True"/>
                                <field name="tipe_rekening_bank"/>
                            </list>
                            <form string="Bank Account">
                                <sheet>
                                    <group>
                                        <field name="acc_number"/>
                                        <field name="bank_id"/>
                                        <field name="allow_out_payment" string="Send Money"/>
                                        <field name="tipe_rekening_bank"/>
                                    </group>
                                </sheet>
                            </form>
                        </field>
                    </group>
                </page>
            </xpath> -->

            <xpath expr="//page[@name='sales_purchases']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>

            <xpath expr="//page[@name='internal_notes']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>

            <xpath expr="//group/div/field[@name='followup_reminder_type']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>
            <xpath expr="//group/field[@name='followup_next_action_date']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>
            <xpath expr="//group/field[@name='followup_responsible_id']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>
            <xpath expr="//group/div/a[@name='%(account_followup.manual_reminder_action)d']" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>

        </field>
    </record>

    <record id="view_inherit_vendor" model="ir.ui.view">
        <field name="name">view.inherit.vendor</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account_asset_extension.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='efaktur']" position="attributes">
                <attribute name="invisible">context.get('vendor_view')</attribute>
            </xpath>
        </field>
    </record>

    <!-- <record model="ir.ui.view" id="view_partner_property_form_followup_hide">
        <field name="name">res.partner.property.form.followup.hide</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account_followup.view_partner_property_form_followup"/>
        <field name="arch" type="xml">
            <xpath expr="//group/div/field[@name='followup_reminder_type']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>
            <xpath expr="//group/field[@name='followup_next_action_date']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>
            <xpath expr="//group/field[@name='followup_responsible_id']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>
            <xpath expr="//group/div/a[@name='%(account_followup.manual_reminder_action)d']" position="attributes">
                <attribute name="invisible">True</attribute>
            </xpath>
        </field>
    </record> -->

    <record id="view_partner_property_form_followup_hide" model="ir.ui.view">
        <field name="name">res.partner.property.form.followup.hide</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.view_partner_property_form"/>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='bank_ids']/list/field[@name='allow_out_payment']" position="after">
                <field name="bank_id" />
                <field name="account_number" />
                <field name="tipe_rekening_bank" />
            </xpath>

            <xpath expr="//field[@name='bank_ids']/list/field[@name='acc_number']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='bank_ids']/list/field[@name='bank_id']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='bank_ids']/list/field[@name='allow_out_payment']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='bank_ids']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='property_account_payable_id']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='property_account_receivable_id']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='autopost_bills']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='ignore_abnormal_invoice_date']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='ignore_abnormal_invoice_amount']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//field[@name='invoice_sending_method']" position="attributes">
                <attribute name="readonly">context.get('vendor_view') and vendor_state != 'draft'</attribute>
            </xpath>

            <xpath expr="//page[@name='accounting']" position="after">

                <page string="Approval Authorized" name="approval_prospective" invisible="not context.get('vendor_view')">
                    <field name="approval_ids" mode="list" readonly='1' force_save='1' >
                        <list>
                            <field name="level"/>
                            <field name="approval_by"/>
                            <field name="department_ids" widget="many2many_tags"/>
                            <field name="job_ids" widget="many2many_tags"/>
                            <field name="job_level"/>
                            <field name="employee_ids" widget="many2many_tags"/>
                            <field name="approval_type"/>
                            <field name="voting_point"/>
                            <field name="total_voting_point"/>
                            <field name="state"/>
                        </list>
                        <form>
                            <sheet>
                                <group>
                                    <field name="level"/>
                                    <field name="approval_by"/>
                                    <field name="department_ids" widget="many2many_tags"/>
                                    <field name="job_ids" widget="many2many_tags"/>
                                    <field name="job_level"/>
                                    <field name="employee_ids" widget="many2many_tags"/>
                                    <field name="approval_type"/>
                                    <field name="voting_point"/>
                                    <field name="total_voting_point"/>
                                    <field name="state"/>
                                </group>
                            </sheet>
                        </form>
                    </field>
                    <!-- <field name="approval_ids" readonly="1" force_save='1' /> -->

                </page>
                <page string="Approval Due Diligence" name="approval_prospective_details" invisible="not context.get('vendor_view')">
                    <field name="can_edit_due_diligence" invisible="1"/>
                    <field name="procurement_unit" invisible="1"/>
                    <field name="fraud_unit" invisible="1"/>
                    <field name="compilance_unit" invisible="1"/>
                    <field name="hc_unit" invisible="1"/>
                    <field name="is_all_detail_filled" invisible="1"/>
                    <field name="approval_detail_ids" mode="list" readonly='can_edit_due_diligence == False ' force_save='1' >
                        <list editable="bottom">
                            <field name="procurement_unit" column_invisible="1"/>
                            <field name="fraud_unit" column_invisible="1"/>
                            <field name="compilance_unit" column_invisible="1"/>
                            <field name="procurement_unit" column_invisible="1"/>
                            <!-- <field name="unit_department_due_dill" column_invisible="0"/> -->
                            <field name="sequence" string="No"/>
                            <field name="name" string="Nama" readonly="fraud_unit == True or compilance_unit == True or hc_unit == True"/>
                            <field name="birth_date" string="Tanggal Lahir" readonly="fraud_unit == True or compilance_unit == True or hc_unit == True"/>
                            <field name="bl_internal" string="Procurement BL Internal (Y/N)" readonly="fraud_unit == True or compilance_unit == True or hc_unit == True"/>
                            <field name="media_info" string="Procurement Informasi Negatif Media (Clear/Not)" readonly="fraud_unit == True or compilance_unit == True or hc_unit == True"/>
                            <field name="fraud_data" string="Fraud Data Fraud (Clear/Not)" readonly="procurement_unit == True or compilance_unit == True or hc_unit == True"/>
                            <field name="apu_ppt" string="Compliance APU-PPT (Clear/Not)" readonly="procurement_unit == True or fraud_unit == True or hc_unit == True"/>
                            <field name="employee_status" string="Human Capital Status" readonly="procurement_unit == True or fraud_unit == True or compilance_unit == True"/>
                            <field name="note" readonly="fraud_unit == True or compilance_unit == True or hc_unit == True"/>
                        </list>
                        <!-- <form>
                            <sheet>
                                <group>
                                    <field name="name" required="1"/>
                                    <field name="birth_date"/>
                                    <field name="bl_internal"/>
                                    <field name="media_info"/>
                                    <field name="fraud_data"/>
                                    <field name="apu_ppt"/>
                                    <field name="employee_status"/>
                                    <field name="note" widget="text"/>
                                </group>
                            </sheet>
                        </form> -->
                    </field>
                </page>

                <page string="Attachment" name="attachment" invisible="not context.get('vendor_view')" readonly="context.get('vendor_view') and vendor_state != 'draft'">
                    <field name="attachment_ids" readonly="context.get('vendor_view') and vendor_state != 'draft'">
                        <list editable="bottom" delete='0'>
                            <field name="sequence" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'"/>
                            <field name="req_document" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'"/>
                            <field name="availability" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'"/>
                            <field name="reason_unavailable" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'" widget="text" />
                            <field name="attachment" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'" widget="many2many_binary"/>
                        </list>
                        <form>
                            <group>
                                <group>
                                    <field name="sequence" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'"/>
                                    <field name="req_document" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'"/>
                                    <field name="availability" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'"/>
                                    <field name="reason_unavailable" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'" widget="text" />
                                    <field name="attachment" readonly="context.get('vendor_view') and parent.vendor_state != 'draft'" widget="many2many_binary"/>
                                </group>
                                <group>
                                    
                                </group>
                            </group>
                        </form>
                    </field>
                </page>

                <page string="VP Account" name="vp_account" invisible="1">
                    <form>
                        <sheet>
                            <group>
                                <group>
                                    <field name="vp_account_email"/>
                                    <field name="vp_account_password" password="True"/>
                                </group>
                            </group>
                        </sheet>
                    </form>
                </page>

            </xpath>

        </field>
    </record>

    <record id="view_partner_form_inherit_add_fax" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.add.fax</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/> 
        <field name="arch" type="xml">
            <xpath expr="//page[@name='contact_addresses']//form/sheet/group//field[@name='mobile']" position="after">
                <field name="fax"/>
            </xpath>
            <xpath expr="//page[@name='contact_addresses']//form/sheet/group//field[@name='name']" position="attributes">
                <attribute name="string">First Name</attribute>
                <attribute name="placeholder">First Name</attribute>
            </xpath>
            <xpath expr="//page[@name='contact_addresses']//form/sheet/group//field[@name='name']" position="after">
                <field name="middle_name" placeholder="Middle Name"/>
                <field name="last_name" placeholder="Last Name"/>
            </xpath>
        </field>
    </record>

    <!-- self service -->
    <record id="view_self_service_popup" model="ir.ui.view">
        <field name="name">self.service.popup.form</field>
        <field name="model">res.partner</field>
        <field name="arch" type="xml">
            <form string="Initial Data" create="0">
                <group  class="mb-3">
                    <button
                        name="%(partner_linkaja.action_partner_vp)d"
                        string="Generate Web Link"
                        type="action"
                        context="{'default_partner_id': id}"
                    />
                    <field name="link_vp" force_save='1' invisible='1' />
                    <field name="name" />
                    <field name="email"/>
                    <field name="tax_organization_type" />
                    <field name="busines_category"/>
                </group>
            </form>
        </field>
    </record>

</odoo>