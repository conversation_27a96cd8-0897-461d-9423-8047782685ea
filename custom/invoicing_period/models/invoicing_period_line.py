# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class InvoicingPeriodLine(models.Model):
    _name = 'invoicing.period.line'
    _description = "Invoicing Period Line"

    name = fields.Char(string="Period Name", required=True)
    invoice_period_id = fields.Many2one('invoicing.period', string="Period", required=True, copy=False)
    date_start = fields.Date(string="Period Start", required=True, copy=False)
    date_end = fields.Date(string="Period End", required=True, copy=False)
    close_date = fields.Date(string="Close Date", required=True, copy=False)
    state = fields.Selection([
        ('open', "Open"), ('close', "Close")
    ], string="Status", default='open')
    account_move_ids = fields.One2many('account.move', 'invoicing_period_line_id', string="Invoices/Bills")

    def close_period(self):
        self.ensure_one()
        move_type = self.invoice_period_id.move_type
        # query = '''
        #     SELECT id FROM account_move
        #     WHERE 
        #         state='draft' AND move_type=%s AND
        #         invoice_date >= %s AND invoice_date < %s
        # '''
        # where_params = [move_type, self.date_start, self.date_end]
        # self._cr.execute(query, where_params)
        # unprocessed_move_ids = self._cr.fetchall()
        # if unprocessed_move_ids:
        #     message = ""
        #     if move_type == 'out_invoice':
        #         message = "There are Customer Invoices that are still in 'Draft' status during this period."
        #     elif move_type == 'in_invoice':
        #         message = "There are Vendor Bill that are still in 'Draft' status during this period."

        #     raise ValidationError(message)

        self.state = 'close'

    def reopen_period(self):
        # search_period = [
        #     ('date_start', '<=', self.date_start),
        #     ('date_stop', '>=', self.date_end),
        #     ('company_id', '!=', False),
        # ]
        # journals = self.env['account.period'].search(search_period)
        # if journals:
        #     for period in journals:
        #         if period.state == 'done' and period.company_id.id == self.invoice_period_id.company_id.id:
        #             raise ValidationError('Failed reopen period, because GL accounting period closed!')
        self.state = 'open'
