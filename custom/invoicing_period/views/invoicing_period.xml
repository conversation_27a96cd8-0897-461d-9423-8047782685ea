<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="invoice_period_form_view" model="ir.ui.view">
            <field name="name">invoice.period.form.view</field>
            <field name="model">invoicing.period</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="generate_periods" type="object" string="Generate Periods" class="oe_highlight"
                                invisible="period_line_ids or state == 'close'"
                                groups="account.group_account_manager"/>
                        <button name="close_all_lines" type="object" string="Close All" class="oe_highlight" invisible="state != 'open'"
                                groups="account.group_account_manager"/>
                        <button name="reopen_all_lines" type="object" string="Re-open All" class="oe_highlight" invisible="state != 'close'"
                                groups="base.group_system"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name" readonly="state == 'close'" />
                                <field name="move_type" readonly="1" force_save="1"/>
                                <field name="company_id" required="1" readonly="state == 'close'" />
                            </group>
                            <group>
                                <field name="date_start" readonly="state == 'close'"/>
                                <field name="date_end" readonly="state == 'close'"/>
                            </group>
                        </group>
                        <field name="period_line_ids" nolabel="1" readonly="state == 'close'" >
                            <list editable="bottom">
                                <field name="name"/>
                                <field name="date_start"/>
                                <field name="date_end"/>
                                <field name="close_date"/>
                                <field name="state" readonly="1"/>
                            </list>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="invoice_period_tree_view" model="ir.ui.view">
            <field name="name">invoice.period.tree.view</field>
            <field name="model">invoicing.period</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="state"/>
                </list>
            </field>
        </record>

        <record id="invoice_period_action" model="ir.actions.act_window">
            <field name="name">Invoice Periods</field>
            <field name="res_model">invoicing.period</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('move_type', '=', 'out_invoice')]</field>
            <field name="context">{'default_move_type': 'out_invoice'}</field>
        </record>

        <menuitem
            id="invoice_period_menu"
            name="Invoice Periods"
            sequence="0"
            action="invoice_period_action"
            parent="invoicing_period.invoice_period_root_menu"
            groups="account.group_account_manager"
        />


        <record id="bill_period_form_view" model="ir.ui.view">
            <field name="name">bill.period.form.view</field>
            <field name="model">invoicing.period</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="generate_periods" type="object" string="Generate Periods" class="oe_highlight"
                                invisible="period_line_ids or state == 'close'"
                                groups="account.group_account_manager"/>
                        <button name="close_all_lines" type="object" string="Close All" class="oe_highlight" invisible="state != 'open'"
                                groups="account.group_account_manager"/>
                        <button name="reopen_all_lines" type="object" string="Re-open All" class="oe_highlight" invisible="state != 'close'"
                                groups="base.group_system"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name" readonly="state == 'close'"/>
                                <field name="move_type" readonly="1" force_save="1"/>
                                <field name="company_id" readonly="state == 'close'"/>
                            </group>
                            <group>
                                <field name="date_start" readonly="state == 'close'"/>
                                <field name="date_end" readonly="state == 'close'"/>
                            </group>
                        </group>
                        <field name="period_line_ids" nolabel="1" readonly="state == 'close'">
                            <list>
                                <field name="name"/>
                                <field name="date_start"/>
                                <field name="date_end"/>
                                <field name="close_date"/>
                                <field name="state" readonly="1"/>
                            </list>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="bill_period_tree_view" model="ir.ui.view">
            <field name="name">bill.period.tree.view</field>
            <field name="model">invoicing.period</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="state"/>
                </list>
            </field>
        </record>

        <record id="bill_period_action" model="ir.actions.act_window">
            <field name="name">Invoice Periods</field>
            <field name="res_model">invoicing.period</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('move_type', '=', 'in_invoice')]</field>
            <field name="context">{'default_move_type': 'in_invoice'}</field>
        </record>

        <menuitem
            id="bill_period_menu"
            name="Bill Periods"
            sequence="0"
            action="bill_period_action"
            parent="invoicing_period.bill_period_root_menu"
            groups="account.group_account_manager"
        />

    </data>
</odoo>
