<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="invoicing_period_line_form_view" model="ir.ui.view">
            <field name="name">invoicing.period.line.form.view</field>
            <field name="model">invoicing.period.line</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button name="close_period" type="object" string="Close Period" class="oe_highlight" invisible="state != 'open'"
                                groups="account.group_account_manager"/>
                        <button name="reopen_period" type="object" string="Re-open Period" class="oe_highlight" invisible="state != 'close'"
                                groups="base.group_system"/>
                        <field name="state" widget="statusbar"/>
                    </header>
                    <sheet>
                        <group>
                            <group>
                                <field name="name" readonly="state == 'close'" />
                                <field name="invoice_period_id" readonly="state == 'close'" />
                            </group>
                            <group>
                                <field name="date_start" readonly="state == 'close'" />
                                <field name="date_end" readonly="state == 'close'" />
                                <field name="close_date" readonly="state == 'close'" />
                            </group>
                        </group>
                        <field name="account_move_ids" nolabel="1" force_save="1">
                            <list create="0" edit="0" delete="0">
                                <field name="name"/>
<!--                                <field name="partner_id"/>-->
<!--                                <field name="invoice_date"/>-->
<!--                                <field name="amount_total" widget="monetary" options="{'currency_field': 'currency_id'}"/>-->
<!--                                <field name="currency_id" invisible="1"/>-->
                            </list>
                        </field>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="invoicing_period_line_tree_view" model="ir.ui.view">
            <field name="name">invoicing.period.line.tree.view</field>
            <field name="model">invoicing.period.line</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="close_date"/>
                    <field name="state"/>
                    <field name="invoice_period_id" invisible="1"/>
                </list>
            </field>
        </record>

        <record id="invoice_period_line_action" model="ir.actions.act_window">
            <field name="name">Periods</field>
            <field name="res_model">invoicing.period.line</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('invoice_period_id.move_type', '=', 'out_invoice')]</field>
            <field name="context">{}</field>
        </record>

        <menuitem
            id="invoice_period_line_menu"
            name="Periods"
            sequence="1"
            action="invoice_period_line_action"
            parent="invoicing_period.invoice_period_root_menu"
            groups="account.group_account_manager"
        />

        <record id="bill_period_line_action" model="ir.actions.act_window">
            <field name="name">Periods</field>
            <field name="res_model">invoicing.period.line</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('invoice_period_id.move_type', '=', 'in_invoice')]</field>
            <field name="context">{}</field>
        </record>

        <menuitem
            id="bill_period_line_menu"
            name="Periods"
            sequence="1"
            action="bill_period_line_action"
            parent="invoicing_period.bill_period_root_menu"
            groups="account.group_account_manager"
        />

    </data>
</odoo>