<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_account_justification_form" model="ir.ui.view">
            <field name="name">view account.justification inherited</field>
            <field name="model">account.justification</field>
            <field name="inherit_id" ref="account_budget_justif.view_account_justification_form" />
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="action_to_draft" string="Set to Draft" type="object" invisible="state not in ['pending_approval', 'return', 'rejected', 'cancel', 'done']"/>
                </xpath>
                <xpath expr="//button[@name='action_cancel']" position="attributes">
                    <attribute name="invisible">state not in ['draft', 'return','approved', 'pending_approval']</attribute>
                </xpath>
            </field>
        </record>

    </data> 
</odoo>
