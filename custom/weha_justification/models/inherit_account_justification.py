
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError



class WehaAccountJustification(models.Model):
    _inherit = 'account.justification'


    def action_to_draft(self):
        self.state = 'draft'
    

