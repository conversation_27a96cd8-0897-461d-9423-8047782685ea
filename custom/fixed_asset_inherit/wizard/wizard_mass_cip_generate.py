from odoo import fields, api, models, _, Command
from datetime import datetime
from odoo.exceptions import UserError


class WizardMassCipGenerate(models.TransientModel):
    _name = 'wizard.mass.cip.generate'

    name = fields.Char('Name')
    cip_id = fields.Many2one('cip.configuration', string='CIP')
    company_id = fields.Many2one('res.company', string='Company')
    analytic_account_id = fields.Many2one('account.analytic.account', string='Analityc Account')
    date_acquisition = fields.Date('Acquisition Date')
    amount_total = fields.Float('Total')
    is_post_cip = fields.Boolean('Is Post Cip', default=False)
    model_id = fields.Many2one('account.asset', string='Asset Major')
    asset_group_id = fields.Many2one('account.asset.group', string='Asset Minor')
    stock_location_id = fields.Many2one('asset.location', string='Location')
    line_ids = fields.One2many('wizard.mass.cip.detail', 'generate_id', string='line')
    journal_id = fields.Many2one('account.journal', string='Journal')
    type = fields.Selection([
        ('merge', 'Merge'),
        ('split', 'Split'),
    ], string='type')
    is_selected_all = fields.Boolean('Select All Lines', default=False)

    @api.model
    def default_get(self, fields):
        res = {}
        lines = []
        sql = """
            SELECT sp.id as sp_id, sm.id as sm_id, am.id as am_id from stock_move sm LEFT JOIN stock_picking sp ON sp.id = sm.picking_id 
                LEFT JOIN stock_picking_type spt ON spt.id = sp.picking_type_id 
                LEFT JOIN product_product pp ON pp.id = sm.product_id
                LEFT JOIN product_template pt ON pt.id = pp.product_tmpl_id
                LEFT JOIN account_move am ON am.picking_journal_bill_id = sp.id
                WHERE spt.code = 'incoming' AND sp.state_fppr = 'ap_invoice' AND sp.purchase_dua_id notnull AND 
                sm.is_cip isnull AND am.state = 'posted' AND pt.is_cip <> false
            """
        self.env.cr.execute(sql)
        picking_lines = self.env.cr.dictfetchall()

        for picking in picking_lines:
            bill = self.env['account.move'].browse(picking['am_id'])
            stock_move = self.env['stock.move'].browse(picking['sm_id'])
            picking_id = self.env['stock.picking'].browse(picking['sp_id'])
            purchase_id = picking_id.purchase_dua_id
            purchase_line_number = 0
            quantity = 0
            price_unit = 0
            currency_id = False
            currency_rate = 1

            if purchase_id:
                po_lines = purchase_id.order_line.filtered(lambda l: l.product_id == stock_move.product_id)
                purchase_line_number = min(po_lines.mapped('sequence') or [0])

            for line in bill.invoice_line_ids:
                if line.product_template_id == stock_move.product_tmpl_id:
                    quantity = line.quantity
                    price_unit = line.price_unit
                    currency_id = line.currency_conversion_id.id
                    currency_rate = line.inverse_conv_rate

            data = {
                'selected': False,
                'product_tmpl_id': stock_move.product_tmpl_id.id,
                'purchase_id': purchase_id.id if purchase_id else False,
                'picking_id': picking_id.id,
                'date_picking': picking_id.scheduled_date,
                # 'analytic_account_id': 30,
                'invoice_id': bill.id,
                # 'invoice_line_id': 1,
                'qty': quantity,
                'price_unit': price_unit,
                'amount': quantity * price_unit,
                # 'purchase_line_number': purchase_line_number,
                'model_id': stock_move.product_tmpl_id.asset_model_id.id,
                'stock_move_id': stock_move.id,
                'currency_id': currency_id,
                'currency_rate': currency_rate,
                'from_receipt': True,
            }
            lines.append((0, 0, data))

        sql = """
            SELECT afl.id as afl_id from account_fpjp_line afl LEFT JOIN account_fpjp af ON af.id = afl.account_fpjp_id
                WHERE afl.type = 'cip' AND af.state = 'done' AND afl.is_cip is null
            """
        self.env.cr.execute(sql)
        fpjp_lines = self.env.cr.dictfetchall()
        # fpjp_lines = self.env['account.fpjp.line'].search([('type', '=', 'asset'), ('account_fpjp_id.state', '=', 'done')])
        for line in fpjp_lines:
            fpjp = self.env['account.fpjp.line'].browse(line['afl_id'])
            is_posted = True
            bill = False
            product_id = False
            quantity = 0
            price_unit = 0
            currency_id = False
            currency_rate = 1
            for bill_no in fpjp.bill_line_ids:
                bill = bill_no.move_id.id
                product_id = bill_no.product_template_id
                quantity = bill_no.quantity
                price_unit = bill_no.price_unit
                currency_id = bill_no.currency_conversion_id.id
                currency_rate = bill_no.inverse_conv_rate
                if bill_no.move_id.state != 'posted':
                    is_posted = False
                    break
            # if is_posted and product_id:
            data = {
                'selected': False,
                'product_tmpl_id': product_id,
                'model_id': product_id.asset_model_id.id if product_id and product_id.asset_model_id else False,
                'date_picking': fpjp.account_fpjp_id.fpjp_date,
                'invoice_id': bill,
                'qty': quantity,
                'price_unit': price_unit,
                'amount': quantity * price_unit,
                'from_fpjp': True,
                'fpjp_line_id': fpjp.id,
                'fpjp_id': fpjp.account_fpjp_id.id,
                'desc_fpjp': fpjp.name,
                'currency_id': currency_id,
                'currency_rate': currency_rate,
            }
            lines.append((0, 0, data))

        res['line_ids'] = lines
        # res['asset_line_ids'] = lines

        # default journal
        # journal_id = self.env['account.journal'].search([
        #     ('type', '=', 'general'),
        # ], limit=1)
        # res['journal_id'] = journal_id.id

        return res

    @api.onchange('date_acquisition')
    def _onchange_date_acquisition(self):
        if self.date_acquisition:
            for line in self.line_ids:
                line.date_acquisition = self.date_acquisition

    @api.onchange('model_id')
    def _onchange_model_id(self):
        self.journal_id = self.model_id.journal_id.id
        if self.model_id:
            for line in self.line_ids:
                line.model_id = self.model_id.id

    @api.onchange('asset_group_id')
    def _onchange_asset_group_id(self):
        if self.asset_group_id:
            for line in self.line_ids:
                line.asset_group_id = self.asset_group_id.id

    @api.onchange('stock_location_id')
    def _onchange_stock_location_id(self):
        if self.stock_location_id:
            for line in self.line_ids:
                line.stock_location_id = self.stock_location_id.id

    @api.onchange('is_selected_all', 'line_ids', 'line_ids.selected', 'line_ids.amount')
    def onchange_amount_total(self):
        amount_total = 0
        for line in self.line_ids.filtered(lambda x: x.selected):
            amount_total += line.amount * line.currency_rate
        self.amount_total = amount_total

    @api.onchange('is_selected_all')
    def onchange_selected_all(self):
        if self.is_selected_all:
            for line in self.line_ids:
                line.selected = True

    def _prepare_move_line_expense(self, detail):
        lines = [Command.create({
            'account_id': self.cip_id.account_id.id,
            'debit': 0,
            'credit': detail.amount,
            'name': detail.desc_fpjp or self.cip_id.name,
            'date': fields.Date.today(),
        })]
        lines += [Command.create({
            'account_id': detail.expense_account_id.id,
            'debit': detail.amount,
            'credit': 0,
            'name': detail.desc_fpjp or self.cip_id.name,
            'date': fields.Date.today(),
        })]
        return lines

    def create_expense(self, details, journal):
        for detail in details:
            move_dict = {
                'journal_id': journal,
                'date': fields.Date.today(),
                'line_ids': self._prepare_move_line_expense(detail),
            }
            move = self.env['account.move'].create(move_dict)
            move._post() 
            # Set Is CIP to True
            if detail.stock_move_id:
                detail.stock_move_id.is_cip = True
            if detail.fpjp_line_id:
                detail.fpjp_line_id.is_cip = True

    def button_generate(self):
        """ function to generate assets """
        details = self.line_ids.filtered(lambda x: x.selected and not x.is_expense)
        journal = self.journal_id.id  # always use the journal

        # Create Expenses
        expenses = self.line_ids.filtered(lambda x: x.is_expense)
        self.create_expense(expenses, journal)

        name = self.name
        product = details[0].product_tmpl_id
        picking = details[0].picking_id
        source_line = []
        qty = 0
        major_id = False
        current_project = False
        for detail in details:
            project = detail.purchase_id.phase_project_id or detail.fpjp_id.phase_project_id
            if current_project and project:
                if project != current_project:
                    raise UserError(_("All selected lines must have the same project."))
            current_project = project
            vals = {
                # 'invoice_id': detail.invoice_id.id,
                # 'purchase_id': detail.purchase_id.id,
                'invoice_name': detail.invoice_id.name,
                'purchase_name': detail.purchase_id.name,
                # 'invoice_line_number': detail.line_number,
                # 'purchase_line_number': detail.purchase_line_number,
                'description': detail.product_tmpl_id.display_name,
                'amount': detail.amount,
                'product_id': detail.product_tmpl_id.id,
            }
            source_line.append((0, 0, vals))
            qty += detail.qty
            if detail.stock_move_id:
                detail.stock_move_id.is_cip = True
            if detail.fpjp_line_id:
                detail.fpjp_line_id.is_cip = True
            major_id = detail.model_id
        total = sum(x.amount * x.currency_rate for x in details)
        phase_project_id = self.line_ids.mapped('fpjp_id.phase_project_id.id') or self.line_ids.mapped('purchase_id.phase_project_id')
        data = {
            'name': name,
            'qty': qty,
            'picking_id': picking.id,
            'model_id': major_id.id if major_id else False,
            'asset_group_id': self.asset_group_id.id if self.asset_group_id else False,
            'stock_location_id': self.stock_location_id.id if self.stock_location_id else False,
            'account_asset_id': major_id.account_asset_id.id if major_id else False,
            'original_value': total,
            'acquisition_date': self.date_acquisition,
            'date_received': self.date_acquisition,
            'state': 'draft',
            'asset_type': 'purchase',
            'cip_id': self.cip_id.id,
            # 'phase_project_cip_id': self.fpjp_id.phase_project_cip_id.id or self.p,
            'phase_project_cip_id': phase_project_id[0] if phase_project_id else False,
            # 'origin_ids': [(4, x.invoice_line_id.id) for x in details],
            # 'source_line_ids': source_line,
        }

        # create data, call _onchange_model_id, then write cache
        new_asset = self.env['account.asset.cip'].new(data)
        new_asset._onchange_model_id()  # to assign journal and accounts
        vals = new_asset._convert_to_write(new_asset._cache)
        asset = self.env['account.asset.cip'].create(vals)
        asset.with_context({
            'name': self.name,
            'debit_account_id': self.cip_id.account_id.id,
            'credit_account_id': product.property_account_expense_id.id,
            'product_id': product,
            'journal_id': journal,
            'from_cip': True,
        }).action_move_create(False, date_acc=self.date_acquisition)

        return True


class WizardMassCipDetail(models.TransientModel):
    _name = 'wizard.mass.cip.detail'

    @api.onchange('selected')
    def _onchange_selected(self):
        if self.selected:
            self.is_expense = False

    @api.onchange('is_expense')
    def _onchange_is_expense(self):
        if self.is_expense:
            self.selected = False

    selected = fields.Boolean('Selected', default=False)
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id.id)
    amount = fields.Monetary('Acquisition Value', currency_field='currency_id')
    purchase_id = fields.Many2one('purchase.order', string='Purchase Order')
    partner_id = fields.Many2one('res.partner', string='Vendor')
    picking_id = fields.Many2one('stock.picking', string='Receipt. Ref')
    date_picking = fields.Date('Receipt Date', required=False)
    qty = fields.Float('Qty')
    product_id = fields.Many2one('product.product', string='Product')
    stock_move_id = fields.Many2one('stock.move', 'Stock Move')
    invoice_id = fields.Many2one('account.move', 'Invoice', required=False)
    date_acquisition = fields.Date('Acquisition Date')
    model_id = fields.Many2one('account.asset', string='Asset Major')
    asset_group_id = fields.Many2one('account.asset.group', string='Asset Minor')
    stock_location_id = fields.Many2one('asset.location', string='Location')
    purchase_line_number = fields.Integer('Purchase Line Number')
    move_line_id = fields.Many2one('account.move.line', string='Move Line')
    analytic_account_id = fields.Many2one('account.analytic.account', string='Analityc Account')
    cip_id = fields.Many2one('cip.configuration', string='CIP')
    price_unit = fields.Float('Price Unit')
    product_code = fields.Char('Product Code')
    company_id = fields.Many2one('res.company', string='company')
    generate_id = fields.Many2one('wizard.mass.cip.generate', string='generate')
    from_receipt = fields.Boolean('From Receipt')
    from_fpjp = fields.Boolean('From FPJP')
    fpjp_line_id = fields.Many2one('account.fpjp.line', 'FPJP Line')
    fpjp_id = fields.Many2one('account.fpjp', 'FPJP')
    desc_fpjp = fields.Char('Description FPJP')
    product_tmpl_id = fields.Many2one('product.template', 'Product')
    currency_rate = fields.Float(string='Rate')
    is_expense = fields.Boolean('Is Expense')
    expense_account_id = fields.Many2one('account.account', string='Expense Account')
