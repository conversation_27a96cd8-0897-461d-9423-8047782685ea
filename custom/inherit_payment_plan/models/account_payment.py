from odoo import fields, api, models


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    is_plan_faid = fields.Bo<PERSON>an('Is PLan Faid', default=False)
    is_plan_failed = fields.Boolean('Is PLan Failed', default=False)
    state = fields.Selection(selection_add = [
        ('draft', 'Draft'),
        ('plan_failed', 'Plain Failed')
    ], 
    ondelete={'plan_failed': 'set default'})
    is_flag_pp = fields.Boolean('Is Flag Payment Plan', compute='_compute_is_flag_pp', store=True)

    @api.constrains('payment_method_line_id')
    def _check_payment_method_line_id(self):
        pass

    @api.depends('payment_plan_id')
    def _compute_is_flag_pp(self):
        for rec in self:
            is_flag_pp = False
            if rec.payment_plan_id and rec.payment_plan_id.pay_groups_id and rec.payment_plan_id.pay_groups_id.flag:
                is_flag_pp = True
            rec.is_flag_pp = is_flag_pp

    def action_validate(self):
        res = super(AccountPayment, self).action_validate()

        for rec in self:
            if rec.move_id:
                if rec.memo:
                    rec.move_id.ref = rec.memo
                else:
                    rec.move_id.ref = rec.name 
            if rec.state in 'paid':
                domain = [('account_type', 'in', ('asset_receivable', 'liability_payable')), ('reconciled', '=', False)]
                to_reconcile = []
                for invoice in rec.payment_invoice_ids:
                    for inv_line in invoice.move_id.line_ids.filtered_domain(domain):
                        to_reconcile.append(inv_line)
                    if invoice.move_id.amount_residual_signed != 0:
                        if abs(invoice.move_id.amount_residual_signed) == abs(invoice.move_id.amount_total_signed):
                            invoice.move_id.payment_state = 'not_paid'
                        else:
                            invoice.move_id.payment_state = 'partial'
                    elif invoice.move_id.amount_residual_signed == 0:
                        # invoice.move_id.payment_state = 'in_payment'
                        for payment in invoice.payment_id.move_id.line_ids:
                            if payment.reconciled is True:
                                if invoice.payment_id.destination_account_id != payment.account_id:
                                    invoice.move_id.payment_state = 'in_payment'
                                # elif invoice.payment_id.date_bank_statement is False:
                                #     invoice.move_id.payment_state = 'in_payment'
                                else:
                                    # invoice.move_id.with_context(pass_validation=True).payment_state = 'paid'
                                    invoice.move_id.payment_state = 'paid'
                            elif payment.reconciled is False:
                                invoice.move_id.payment_state = 'in_payment'
                    # else:
                    #     invoice.move_id.payment_state = 'not_paid'

                for lines in to_reconcile:
                    payment_lines = rec.move_id.line_ids.filtered_domain(domain)
                    for account in payment_lines.account_id:
                        (payment_lines + lines).filtered_domain([
                            ('account_id', '=', account.id),
                            ('reconciled', '=', False)
                        ]).with_context(no_exchange_difference=True).reconcile()

                # for lines in to_reconcile:
                #     payment_lines = rec.line_ids.filtered_domain(domain)
                #     for account in payment_lines.account_id:
                #         for matching in payment_lines.full_reconcile_id:
                #             (payment_lines + lines).filtered_domain([
                #                 ('account_id', '=', account.id),
                #                 ('full_reconcile_id', '=', matching.id),
                #                 ('reconciled', '=', False)
                #             ]).reconcile()

            # if rec.payment_doc_id:
            #     rec.payment_doc_id.payment_id = rec.id
            #
            # if rec.giro_id:
            #     rec.giro_id.payment_id = rec.id
        return res

    