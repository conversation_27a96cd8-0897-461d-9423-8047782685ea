
# -*- coding: utf-8 -*-
###############################################################################
#    License, author and contributors information in:                         #
#    __manifest__.py file at the root folder of this module.                  #
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from odoo.tools import DEFAULT_SERVER_DATE_FORMAT as DATE_FORMAT, DEFAULT_SERVER_DATETIME_FORMAT as DATETIME_FORMAT
from datetime import datetime
from dateutil.relativedelta import *
from odoo.tools.float_utils import float_is_zero


class WehaPurchaseOrder(models.Model):
    _inherit = 'purchase.order'



    @api.depends('latest_delivery_date')
    @api.onchange('latest_delivery_date')
    def _compute_validity_period(self):
        for record in self:
            # date_purchase_obj = datetime.strptime(str(record.date_purchase), DATE_FORMAT)
            if record.latest_delivery_date:
                record.validity_period = record.latest_delivery_date + relativedelta(days=30)
            else:
                record.validity_period = False

    @api.depends('transaction_date')
    def _compute_po_date(self):
        for record in self:
            if record.transaction_date:
                record.date_purchase = record.transaction_date
            else:
                record.date_purchase = False


    @api.depends('new_picking_ids.state_fppr', 'new_picking_ids')
    def _compute_receipt_status(self):
        for order in self:
            picking_ids = order.new_picking_ids
            if not picking_ids or all(p.state_fppr == 'cancel' for p in picking_ids):
                order.receipt_status = 'pending'  # Not Received (when PO doesn't have any GR/Receipt)
            elif all(p.state_fppr in ['ap_invoice'] for p in picking_ids if p.state_fppr != 'cancel'):
                order.receipt_status = 'full'     # Fully Received (all GR/Receipt are in AP Invoice state)
            elif any(p.state_fppr in ['ap_invoice'] for p in picking_ids):
                order.receipt_status = 'partial'  # Partially Received (some are in AP Invoice state, but not all)
            else:
                order.receipt_status = 'pending'  # Not Received (default case)
        
    @api.depends('new_state', 'order_line.qty_to_invoice', 'new_picking_ids.state_fppr', 'new_picking_ids')
    def _get_invoiced(self):
        precision = self.env['decimal.precision'].precision_get('Product Unit of Measure')
        for order in self:
            picking_ids = order.new_picking_ids
            bill_ids = self.env['account.move'].search([('is_journal_bill', '=', True), ('purchase_order_id', '=', order.id)])
            
            # Nothing to Bill: When there are no GR/Receipt and Bills
            if not picking_ids or all(p.state_fppr == 'cancel' for p in picking_ids):
                order.invoice_status = 'no'
            # Fully Billed: When all GR receipts are in AP Invoice state and all are billed
            elif all(p.state_fppr == 'ap_invoice' for p in picking_ids if p.state_fppr != 'cancel') and bill_ids:
                order.invoice_status = 'invoiced'
            # Waiting Bills: When GR/Receipt exists but not all approved (states: Draft, Pending Approval)
            elif any(p.state_fppr in ['draft', 'pending_approval'] for p in picking_ids):
                order.invoice_status = 'to invoice'
            else:
                order.invoice_status = 'no'

        
    invoice_status = fields.Selection([
        ('no', 'Nothing to Bill'),
        ('to invoice', 'Waiting Bills'),
        ('invoiced', 'Fully Billed'),
    ], string='Billing Status', compute='_get_invoiced', store=True, readonly=True, copy=False, default='no')
    receipt_status = fields.Selection([
        ('pending', 'Not Received'),
        ('partial', 'Partially Received'),
        ('full', 'Fully Received'),
    ], string='Receipt Status', compute='_compute_receipt_status',store=True)
    date_purchase = fields.Date(string='PO Date', compute='_compute_po_date')
    validity_period = fields.Date(string='PO Validity Period', compute='_compute_validity_period' )
    delivery_location_id = fields.Many2one(
        comodel_name="stock.location",
        string="Delivery Location",
        help="Specify the delivery location for this transaction.",
        domain=[('usage','=', 'internal')],
        copy=False)
    

    # @api.onchange('date_purchase')
    # def _onchange_po_validity(self):
    #     if self.new_state == "open":
    #         date_val = self.date_purchase + relativedelta(days=30)
    #         self.validity_periode = date_val
    

    @api.onchange('bidding_rfq_id')
    def _onchange_rfq(self):
        for rec in self.bidding_rfq_id:
            self.partner_id = rec.partner_id.id
            contact_id = self.env['res.partner'].search([('parent_id', '=', rec.partner_id.id)], limit=1)
            self.partner_contact_id = contact_id.id
            self.email_contact = contact_id.email

    def button_confirm(self):
        for order in self:
            if order.order_line.filtered(lambda x: x.price_unit == 0):
                raise ValidationError('Unit Price tidak boleh 0. Mohon hapus line tersebut terlebih dahulu')
            if order.new_state == 'draft' or order.new_state == 'return':
                for order_line in order.order_line:
                    if order_line.product_qty == 0:
                        raise UserError(_("Quantity Product pada Purchase Order tidak boleh 0, mohon hapus terlebih dahulu"))
                order.new_state = 'pending_approval'
                return
            if order.new_state == 'pending_approval':
                order.new_state = 'pending_acknowledge'
                return
            if order.new_state == 'pending_acknowledge':
                order.new_state = 'open'
                order.supplier_reacknowledge = False
                res = super(PurchaseOrder, self).button_confirm()
                return res


    

