<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_move_form" model="ir.ui.view">
            <field name="name">account.move.loa.form</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <!-- add button to do action -->
                <xpath expr="//header" position="inside">
                    <button name="action_approval" type="object" string="Approve" class="btn btn-success"
                        invisible="move_type != 'in_invoice' or validation_state != 'pending' or not is_current_approver"
                        context="{'approve': True}" />
                    <button name="action_approval" type="object" string="Reject" class="btn-danger"
                        invisible="move_type != 'in_invoice' or validation_state != 'pending' or not is_current_approver"
                        context="{'reject': True}" />
                    <button name="action_approval" type="object" string="Reassign" class="btn btn-success"
                        invisible="move_type != 'in_invoice' or validation_state != 'pending' or (not is_current_approver and not is_admin)"
                        context="{'reassign': True}" />
                    <button name="action_return" type="object" string="Return" class="btn btn-success"
                        invisible="move_type != 'in_invoice' or validation_state != 'pending'" />
                    <button name="action_resubmit" type="object" string="Submit" class="btn btn-success" 
                        invisible="move_type != 'in_invoice' or validation_state != 'return'" />
                </xpath>

                <!-- add approval_history_ids in new page inside notebook -->
                <xpath expr="//notebook" position="inside">
                    <page name="history" string="Approvals" invisible="move_type != 'in_invoice'">
                        <field name="is_current_approver" invisible="1" />
                        <field name="selected_approver_ids" widget="many2many_tags" invisible="1"/>
                        <field name="approval_history_ids" readonly="1" force_save='1' />
                    </page>
                    <page string="Approval Details" name="history_detail" invisible="move_type != 'in_invoice'">
                        <field name="approval_history_detail_ids" >
                            <list editable="bottom" create="0" edit="0" delete="0">
                                <field name="level" readonly="1" force_save='1' />
                                <field name="employee_id" readonly="1" force_save='1' />
                                <field name="employee_state" readonly="1" force_save='1' />
                                <field name="employee_date" readonly="1" force_save='1' />
                                <field name="employee_note" readonly="1" force_save='1' />
                                <field name="reassign_employee_id" readonly="1" force_save='1' />
                                <field name="reassign_employee_state" readonly="1" force_save='1' />
                                <field name="reassign_employee_date" readonly="1" force_save='1' />
                                <field name="reassign_employee_note" readonly="1" force_save='1' />
                            </list>
                        </field>
                    </page>
                    <page name="approval_message" string="Message Details" invisible="move_type != 'in_invoice'">
                        <field name="approval_message_ids" readonly="1" force_save='1' />
                    </page>
                </xpath>

                <xpath expr="//field[@name='requestor_id']" position="after">
                    <field name="hierarchy_id" readonly="1" force_save='1' invisible="move_type != 'in_invoice'" />
                </xpath>
            </field>
        </record>

        <record id="view_move_form_ext" model="ir.ui.view">
            <field name="name">account.move.form</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="vendor_bill_linkaja.view_move_form_ext"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_approve']" position="replace" />
            </field>
        </record>

        <record id="view_account_bill_filter" model="ir.ui.view">
            <field name="name">view.account.bill.filter</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_account_bill_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="after">
                    <separator/>
                    <filter name="my_approvals" string="My Approvals" domain="[('selected_approver_ids.user_id', '=', uid)]" />
                    <separator/>
                </xpath>
            </field>
        </record>

        <record id="account.action_move_in_invoice_type" model="ir.actions.act_window">
            <field name="context">{'search_default_in_invoice': 1, 'default_move_type': 'in_invoice', 'display_account_trust': True, 'search_default_my_approvals': 1}</field>
        </record>
    </data>
</odoo>
