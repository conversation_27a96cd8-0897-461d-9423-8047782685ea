# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
from datetime import date, timedelta


class TestTimeDepositReport(TransactionCase):

    def setUp(self):
        super(TestTimeDepositReport, self).setUp()
        
        # Create test company
        self.company = self.env['res.company'].create({
            'name': 'Test Company',
        })
        
        # Create test bank
        self.bank = self.env['res.bank'].create({
            'name': 'Test Bank',
            'bic': 'TESTBANK',
        })
        
        # Create test bank accounts
        self.bank_account_bni = self.env['res.partner.bank'].create({
            'acc_number': '**********',
            'bank_id': self.bank.id,
            'partner_id': self.company.partner_id.id,
        })
        
        self.bank_account_bri = self.env['res.partner.bank'].create({
            'acc_number': '**********',
            'bank_id': self.bank.id,
            'partner_id': self.company.partner_id.id,
        })
        
        # Create test journals with SWIFT codes
        self.journal_bni = self.env['account.journal'].create({
            'name': 'BNI Journal',
            'code': 'BNI',
            'type': 'bank',
            'company_id': self.company.id,
            'bank_account_id': self.bank_account_bni.id,
            'swift_code': '********',
            'is_time_deposit': True,
        })
        
        self.journal_bri = self.env['account.journal'].create({
            'name': 'BRI Journal',
            'code': 'BRI',
            'type': 'bank',
            'company_id': self.company.id,
            'bank_account_id': self.bank_account_bri.id,
            'swift_code': '********',
            'is_time_deposit': True,
        })
        
        # Create test time deposits
        self.time_deposit_konven_bni = self.env['time.deposit'].create({
            'name': 'TD001',
            'deposit_type': 'deposit',
            'deposit_product': 'konven',
            'company_id': self.company.id,
            'deposit_partner_bank_id': self.journal_bni.id,
            'open_date': date.today(),
            'maturity_date': date.today() + timedelta(days=30),
            'deposit_original_amount': *********,
            'interest_rate': 5.5,
        })
        
        self.time_deposit_konven_bri = self.env['time.deposit'].create({
            'name': 'TD002',
            'deposit_type': 'deposit',
            'deposit_product': 'konven',
            'company_id': self.company.id,
            'deposit_partner_bank_id': self.journal_bri.id,
            'open_date': date.today(),
            'maturity_date': date.today() + timedelta(days=30),
            'deposit_original_amount': *********,
            'interest_rate': 5.0,
        })
        
        self.time_deposit_syariah = self.env['time.deposit'].create({
            'name': 'TD003',
            'deposit_type': 'deposit',
            'deposit_product': 'syariah',
            'company_id': self.company.id,
            'deposit_partner_bank_id': self.journal_bni.id,
            'open_date': date.today(),
            'maturity_date': date.today() + timedelta(days=30),
            'deposit_original_amount': *********,
            'interest_rate': 4.5,
        })

        # Create time deposit with extend status (should be excluded)
        self.time_deposit_extend = self.env['time.deposit'].create({
            'name': 'TD004',
            'deposit_type': 'deposit',
            'deposit_product': 'konven',
            'company_id': self.company.id,
            'deposit_partner_bank_id': self.journal_bni.id,
            'open_date': date.today(),
            'maturity_date': date.today() + timedelta(days=30),
            'deposit_original_amount': *********,
            'interest_rate': 6.0,
            'state': 'extend',
        })

    def test_specific_product_filters(self):
        """Test specific product filters work correctly"""
        
        # Test konven filter - should only return BNI deposits
        domain_konven_filter = [
            ('company_id', '=', self.company.id),
            ('deposit_partner_bank_id.swift_code', '=', '********')
        ]
        deposits_konven_filter = self.env['time.deposit'].search(domain_konven_filter)
        self.assertEqual(len(deposits_konven_filter), 2)  # BNI konven + BNI syariah
        
        # Test merchant filter - should only return BRI deposits  
        domain_merchant_filter = [
            ('company_id', '=', self.company.id),
            ('deposit_partner_bank_id.swift_code', '=', '********')
        ]
        deposits_merchant_filter = self.env['time.deposit'].search(domain_merchant_filter)
        self.assertEqual(len(deposits_merchant_filter), 1)  # Only BRI konven
        
        # Verify BRI deposit is not included in konven filter
        bri_deposits_in_konven = deposits_konven_filter.filtered(
            lambda d: d.deposit_partner_bank_id.swift_code == '********'
        )
        self.assertEqual(len(bri_deposits_in_konven), 0)  # Should be 0

    def test_extend_status_exclusion(self):
        """Test that deposits with extend status are excluded"""

        # Test that extend status deposits are excluded from all queries
        domain_all = [
            ('company_id', '=', self.company.id),
            ('state', '!=', 'extend')
        ]
        deposits_no_extend = self.env['time.deposit'].search(domain_all)

        # Should not include the extend deposit
        extend_deposits = deposits_no_extend.filtered(lambda d: d.state == 'extend')
        self.assertEqual(len(extend_deposits), 0)  # Should be 0

        # Verify total count excludes extend deposit
        all_deposits = self.env['time.deposit'].search([('company_id', '=', self.company.id)])
        self.assertEqual(len(all_deposits), 4)  # Total including extend
        self.assertEqual(len(deposits_no_extend), 3)  # Excluding extend
        
    def test_report_wizard_filters(self):
        """Test report wizard filter functionality"""
        
        wizard = self.env['time.deposit.report.wizard'].create({
            'business_unit_id': self.company.id,
            'date_from': date.today(),
            'date_to': date.today(),
            'product': 'all',
            'deposit_type': 'all',
        })
        
        # Test that wizard can be created and has correct default values
        self.assertEqual(wizard.product, 'all')
        self.assertEqual(wizard.deposit_type, 'all')
        self.assertEqual(wizard.business_unit_id, self.company)
