# Time Deposit Report - Total Feature

## Feature Added
Menambahkan kolom total di bawah 4 tabel ketika filter "All" dipilih pada kedua filter (Product dan Deposit Type).

## Implementation

### 1. Total Section Display
Ketika user memilih filter "All" pada kedua filter, di bawah 4 tabel akan ditampilkan:

```
Total Time Deposit  :  [Total Amount]
```

### 2. Raw SQL Query Implementation
Menggunakan raw SQL query untuk menghitung total amount (bukan ORM):

```python
def _write_total_section(self, sheet, workbook, start_row, business_unit_id, date, open_date):
    """Write total section using raw SQL query"""

    # Build SQL query to calculate total amount
    sql_query = """
        SELECT COALESCE(SUM(deposit_original_amount), 0) as total_amount
        FROM time_deposit
        WHERE company_id = %s
        AND open_date >= %s
        AND open_date <= %s
        AND state != 'extend'
    """

    # Execute query with error handling
    try:
        request.env.cr.execute(sql_query, (business_unit_id, date, open_date))
        result = request.env.cr.fetchone()
        total_amount = result[0] if result else 0
    except Exception as e:
        total_amount = 0

    # Create special format for total row
    total_label_format = workbook.add_format({
        'bold': True,
        'border': 1,
        'align': 'left',
        'valign': 'vcenter',
        'bg_color': '#E6E6FA'
    })

    total_colon_format = workbook.add_format({
        'bold': True,
        'border': 1,
        'align': 'center',
        'valign': 'vcenter',
        'bg_color': '#E6E6FA'
    })

    total_amount_format = workbook.add_format({
        'bold': True,
        'border': 1,
        'align': 'right',
        'valign': 'vcenter',
        'num_format': '#,##0.00',
        'bg_color': '#E6E6FA'
    })

    # Write total row
    row = start_row + 2
    sheet.write(row, 0, 'Total Time Deposit', total_label_format)
    sheet.write(row, 1, ':', total_colon_format)
    sheet.write(row, 2, total_amount, total_amount_format)

    return row + 1
```

### 3. Integration
- Method `_write_total_section` dipanggil setelah semua 4 tabel selesai ditulis
- Hanya muncul ketika filter "All" dipilih pada kedua filter
- Menggunakan format number yang sama dengan data amount di tabel

### 4. SQL Query Details
- **Table**: `time_deposit` (simplified, no JOIN needed)
- **Calculation**: `SUM(deposit_original_amount)`
- **Filters Applied**:
  - Company ID sesuai business unit
  - Date range (open_date >= date AND open_date <= open_date)
  - Exclude status 'extend'
- **Safety**: Menggunakan `COALESCE` untuk handle NULL values
- **Error Handling**: Try-catch untuk handle SQL errors

### 5. Display Format
- **Column A**: "Total Time Deposit" (bold, border, background #E6E6FA)
- **Column B**: ":" (bold, border, center align, background #E6E6FA)
- **Column C**: Total amount dengan format number (#,##0.00, bold, border, background #E6E6FA)
- **Position**: 2 baris di bawah tabel terakhir
- **Style**: Background berwarna light purple untuk highlight

## Files Modified
- `controllers/time_deposit_report_controller.py`
  - Added `_write_total_section()` method
  - Modified `_generate_multiple_tables_report()` to call total section

## Behavior
- **Single Filter**: Tidak ada perubahan, total tidak ditampilkan
- **Filter "All"**: Menampilkan 4 tabel + total di bawah
- **Performance**: Menggunakan single SQL query untuk efisiensi

## Example Output
```
[4 Tabel Time Deposit]

Total Time Deposit  :  1,500,000,000.00
```
