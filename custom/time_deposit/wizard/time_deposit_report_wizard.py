# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import io
from datetime import datetime


class TimeDepositReportWizard(models.TransientModel):
    _name = 'time.deposit.report.wizard'
    _description = 'Time Deposit Report Wizard'

    # Filter fields
    business_unit_id = fields.Many2one(
        'res.company', 
        string='Business Unit',
        default=lambda self: self.env.company,
        required=True
    )
    date = fields.Date(
        string='Date',
        required=True,
        default=lambda self: fields.Date.context_today(self).replace(day=1)  # First day of current month
    )
    open_date = fields.Date(
        string='Open Date',
        required=True,
        default=lambda self: fields.Date.context_today(self)
    )
    product = fields.Selection([
        ('all', 'All'),
        ('syariah', 'Syariah'),
        ('konven', 'Konven'),
        ('merchant', 'Merchant')
    ], string='Product', default='all', required=True)

    deposit_type = fields.Selection([
        ('all', 'All'),
        ('ccredit', 'Corporate Credit Card'),
        ('doc', 'DOC'),
        ('deposit', 'Deposit'),
        ('mma', 'MMA'),
        ('optima', 'Optima')
    ], string='Deposit Type', default='all', required=True)

    # Removed date check constraint as requested

    @api.constrains('date', 'open_date')
    def _check_dates(self):
        for record in self:
            if record.date > record.open_date:
                raise UserError(_('Date cannot be greater than Open Date'))

    def action_generate_excel_report(self):
        """Generate Excel report for time deposits"""
        # Build domain for filtering
        domain = [
            ('company_id', '=', self.business_unit_id.id),
            ('state', '!=', 'extend'),  # Exclude deposits with extend status
        ]

        # Only add open_date filters if set
        if self.date:
            domain.append(('open_date', '>=', self.date))
        if self.open_date:
            domain.append(('open_date', '<=', self.open_date))

        # Add product filter
        if self.product != 'all':
            domain.append(('deposit_product', '=', self.product))

        # Add deposit type filter
        if self.deposit_type != 'all':
            domain.append(('deposit_type', '=', self.deposit_type))

        # Get time deposit records
        time_deposits = self.env['time.deposit'].search(domain, order='open_date asc')

        if not time_deposits:
            raise UserError(_('No data found for the selected criteria'))

        # Generate Excel report
        return self._generate_xlsx_report(time_deposits)
    
    def _generate_xlsx_report(self, time_deposits):
        """Generate XLSX report"""
        # Create Excel report using controller
        return {
            'type': 'ir.actions.act_url',
            'url': f'/time_deposit/excel_report?business_unit_id={self.business_unit_id.id}&date={self.date}&open_date={self.open_date}&product={self.product}&deposit_type={self.deposit_type}',
            'target': 'self',
        }
