# -*- coding: utf-8 -*-

from odoo import http
from odoo.http import request, content_disposition
import io
import xlsxwriter
from datetime import datetime


class TimeDepositReportController(http.Controller):

    @http.route('/time_deposit/excel_report', type='http', auth='user', csrf=False)
    def generate_time_deposit_excel_report(self, **kwargs):
        """Generate Excel report for time deposits"""

        # Get parameters from request
        business_unit_id = int(kwargs.get('business_unit_id', 0))
        date_from = kwargs.get('date_from', '')
        date_to = kwargs.get('date_to', '')
        product = kwargs.get('product', 'all')
        deposit_type = kwargs.get('deposit_type', 'all')

        # Build domain for filtering
        domain = [
            ('company_id', '=', business_unit_id),
            ('open_date', '>=', date_from),
            ('open_date', '<=', date_to),
        ]

        # Add product filter
        if product != 'all':
            domain.append(('deposit_product', '=', product))

        # Add deposit type filter
        if deposit_type != 'all':
            domain.append(('deposit_type', '=', deposit_type))
        
        # Get time deposit records
        time_deposits = request.env['time.deposit'].sudo().search(domain, order='open_date asc')

        # Check if no data found
        if not time_deposits:
            # Return a simple Excel with "No data found" message
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            sheet = workbook.add_worksheet('Time Deposit Report')

            cell_format = workbook.add_format({
                'bold': True,
                'align': 'center',
                'valign': 'vcenter'
            })

            sheet.write(0, 0, 'No data found for the selected criteria', cell_format)
            workbook.close()
            output.seek(0)

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'time_deposit_report_{timestamp}.xlsx'

            response = request.make_response(
                output.read(),
                headers=[
                    ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                    ('Content-Disposition', content_disposition(filename))
                ]
            )
            return response

        # Get business unit name
        business_unit = request.env['res.company'].sudo().browse(business_unit_id)
        business_unit_name = business_unit.name if business_unit else ''
        
        # Get product and deposit type labels
        product_dict = dict(request.env['time.deposit']._fields['deposit_product'].selection)
        deposit_type_dict = dict(request.env['time.deposit']._fields['deposit_type'].selection)
        
        product_label = product_dict.get(product, 'All') if product != 'all' else 'All'
        deposit_type_label = deposit_type_dict.get(deposit_type, 'All') if deposit_type != 'all' else 'All'
        
        # Prepare Excel file
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        sheet = workbook.add_worksheet('Time Deposit Report')
        
        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#D3D3D3',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'vcenter'
        })
        
        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })
        
        # Set column widths
        sheet.set_column('A:A', 5)   # No
        sheet.set_column('B:B', 15)  # Deposit Number
        sheet.set_column('C:C', 12)  # Open Date
        sheet.set_column('D:D', 12)  # Mature Date
        sheet.set_column('E:E', 12)  # Deposit In Days
        sheet.set_column('F:F', 15)  # Bank
        sheet.set_column('G:G', 15)  # Amount
        sheet.set_column('H:H', 12)  # Interest Rate
        sheet.set_column('I:I', 15)  # Beneficiary Bank
        sheet.set_column('J:J', 15)  # Bank Account
        sheet.set_column('K:K', 10)  # Type
        sheet.set_column('L:L', 15)  # No Bilyet Deposito
        sheet.set_column('M:M', 12)  # Classification
        sheet.set_column('N:N', 15)  # Remarks
        sheet.set_column('O:O', 12)  # Breakable %
        sheet.set_column('P:P', 15)  # Deposit Status
        
        # Write title
        sheet.merge_range('A1:P1', 'List Time Deposit Report', title_format)

        # Write filter information
        row = 2
        sheet.write(row, 0, f'Business Unit: {business_unit_name}', cell_format)
        row += 1
        sheet.write(row, 0, f'Date From: {date_from}', cell_format)
        row += 1
        sheet.write(row, 0, f'Date To: {date_to}', cell_format)
        row += 1
        sheet.write(row, 0, f'Product: {product_label}', cell_format)
        row += 1
        sheet.write(row, 0, f'Deposit Type: {deposit_type_label}', cell_format)

        # Skip a row
        row += 2

        # Write headers
        headers = [
            'No', 'Deposit Number', 'Open Date', 'Mature Date', 'Deposit In Days',
            'Bank', 'Amount', 'Interest Rate', 'Beneficiary Bank', 'Bank Account',
            'Type', 'No Bilyet Deposito', 'Classification', 'Remarks', 'Breakable %',
            'Deposit Status'
        ]

        for col, header in enumerate(headers):
            sheet.write(row, col, header, header_format)

        # Write data
        row += 1
        for idx, deposit in enumerate(time_deposits, 1):
            sheet.write(row, 0, idx, cell_format)
            sheet.write(row, 1, deposit.name or '', cell_format)

            # Open Date
            if deposit.open_date:
                sheet.write(row, 2, deposit.open_date.strftime('%d-%m-%Y'), cell_format)
            else:
                sheet.write(row, 2, '', cell_format)

            # Mature Date
            if deposit.maturity_date:
                sheet.write(row, 3, deposit.maturity_date.strftime('%d-%m-%Y'), cell_format)
            else:
                sheet.write(row, 3, '', cell_format)

            sheet.write(row, 4, deposit.deposit_in_days_placement or 0, cell_format)
            sheet.write(row, 5, deposit.bank or '', cell_format)
            sheet.write(row, 6, deposit.deposit_original_amount or 0, number_format)
            sheet.write(row, 7, f"{deposit.interest_rate or 0}%", cell_format)

            # Beneficiary Bank
            beneficiary_bank = ''
            if deposit.beneficiary_bank_id and deposit.beneficiary_bank_id.bank_id:
                beneficiary_bank = deposit.beneficiary_bank_id.bank_id.name
            sheet.write(row, 8, beneficiary_bank, cell_format)
            sheet.write(row, 9, deposit.bank_account or '', cell_format)

            # Type (deposit_type)
            sheet.write(row, 10, deposit_type_dict.get(deposit.deposit_type, ''), cell_format)

            sheet.write(row, 11, '', cell_format)  # No Bilyet Deposito - empty for now
            sheet.write(row, 12, deposit.deposit_clasification or '', cell_format)
            sheet.write(row, 13, deposit.deposit_note or '', cell_format)
            sheet.write(row, 14, '', cell_format)  # Breakable % - empty for now

            # Deposit Status (state)
            state_dict = dict(deposit._fields['state'].selection)
            sheet.write(row, 15, state_dict.get(deposit.state, ''), cell_format)

            row += 1

        # Close workbook and prepare response
        workbook.close()
        output.seek(0)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'time_deposit_report_{timestamp}.xlsx'
        
        # Create response
        response = request.make_response(
            output.read(),
            headers=[
                ('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
                ('Content-Disposition', content_disposition(filename))
            ]
        )
        
        return response
