# Time Deposit Report Filter Fix

## Problem Statement
1. **Filter Label Issue**: Pada filter report time deposit dengan product type "konven", title di Excel menampilkan "Deposito Bank Konvensional IDR" padahal seharusnya hanya "Konven"
2. **SWIFT Code Filter Issue**: Data BRI dengan SWIFT Code ******** masih masuk ke hasil filter "konven". Seharusnya:
   - Filter "konven" hanya menampilkan data BNI (SWIFT: ********)
   - Data BRI (SWIFT: ********) seharusnya dikategorikan sebagai "merchant"
3. **Status Filter Issue**: Data dengan deposit status "extend" masih ditampilkan, seharusnya dikecualikan

## Solution Implemented

### 1. Wizard Update (`time_deposit_report_wizard.py`)
- **Tambahan Option "Merchant"**: Menambahkan pilihan "Merchant" pada filter Product
- Sekarang tersedia 4 pilihan: All, Syar<PERSON>, Konven, Merchant

### 2. Controller Logic Fix (`time_deposit_report_controller.py`)

#### Filter Spesifik Logic:
```python
if product == 'konven':
    # For 'konven' filter, only show BNI bank accounts (SWIFT: ********)
    domain.append(('deposit_partner_bank_id.swift_code', '=', '********'))
elif product == 'merchant':
    # For 'merchant' filter, only show BRI bank accounts (SWIFT: ********)
    domain.append(('deposit_partner_bank_id.swift_code', '=', '********'))
elif product == 'syariah':
    domain.append(('deposit_product', '=', 'syariah'))
```

#### Simple Labels (Fixed):
- **Konven**: "Konven" (bukan "Deposito Bank Konvensional IDR")
- **Merchant**: "Merchant" (bukan "Deposito Bank Konvensional Merchant")
- **Syariah**: "Syariah" (bukan "Deposito Bank Syariah")

#### Status Filter:
```python
# Exclude deposits with extend status
('state', '!=', 'extend')
```

### 3. Filter Behavior

#### Before Fix:
- Filter "konven" → Menampilkan semua data dengan `deposit_product = 'konven'` (termasuk BRI)

#### After Fix:
- Filter "konven" → Hanya menampilkan data dengan SWIFT Code ******** (BNI)
- Filter "merchant" → Hanya menampilkan data dengan SWIFT Code ******** (BRI)
- Filter "syariah" → Menampilkan data dengan `deposit_product = 'syariah'`

### 4. Multiple Tables (Filter "All")
Tetap berfungsi seperti sebelumnya dengan 4 tabel:
1. Deposito Bank Konvensional IDR (SWIFT: ********)
2. Deposito Bank Konvensional Merchant (SWIFT: ********)
3. Deposito Bank Syariah (product type: syariah)
4. Deposito USD (placeholder)

## Testing

### Test Cases Added:
1. **test_specific_product_filters()**: Memvalidasi filter konven dan merchant
2. Memastikan BRI tidak masuk ke filter konven
3. Memastikan BNI tidak masuk ke filter merchant

### Manual Testing:
1. Filter "Konven" → Hanya data BNI (SWIFT: ********)
2. Filter "Merchant" → Hanya data BRI (SWIFT: ********)
3. Filter "Syariah" → Data dengan product type syariah
4. Filter "All" → 4 tabel terpisah

## Files Modified:
- `wizard/time_deposit_report_wizard.py`
- `controllers/time_deposit_report_controller.py`
- `tests/test_time_deposit_report.py` (new)

## Impact:
- ✅ Filter "konven" sekarang hanya menampilkan data BNI
- ✅ Data BRI sekarang memiliki filter terpisah "merchant"
- ✅ Label di Excel report sekarang sederhana (Konven, Merchant, Syariah)
- ✅ Data dengan status "extend" dikecualikan dari semua report
- ✅ Backward compatibility untuk filter "All" dan "syariah"
- ✅ Date range filter menggunakan date_from dan date_to
