# Time Deposit Report Filter Fix

## Problem Statement
Pada filter report time deposit dengan product type "konven", data BRI dengan SWIFT Code ******** masih masuk ke hasil filter. Seharusnya:
- Filter "konven" hanya menampilkan data BNI (SWIFT: ********)
- Data BRI (SWIFT: ********) seharusnya dikategorikan sebagai "merchant"

## Solution Implemented

### 1. Wizard Update (`time_deposit_report_wizard.py`)
- **<PERSON><PERSON><PERSON> Option "Merchant"**: Menambahkan pilihan "Merchant" pada filter Product
- Sekarang tersedia 4 pilihan: All, <PERSON>yariah, <PERSON>n<PERSON>, Merchant

### 2. Controller Logic Fix (`time_deposit_report_controller.py`)

#### Filter Spesifik Logic:
```python
if product == 'konven':
    # For 'konven' filter, only show BNI bank accounts (SWIFT: ********)
    domain.append(('deposit_partner_bank_id.swift_code', '=', '********'))
elif product == 'merchant':
    # For 'merchant' filter, only show BRI bank accounts (SWIFT: ********)
    domain.append(('deposit_partner_bank_id.swift_code', '=', '********'))
elif product == 'syariah':
    domain.append(('deposit_product', '=', 'syariah'))
```

#### Custom Labels:
- **Konven**: "Deposito Bank Konvensional IDR"
- **Merchant**: "Deposito Bank Konvensional Merchant"
- **Syariah**: "Deposito Bank Syariah"

### 3. Filter Behavior

#### Before Fix:
- Filter "konven" → Menampilkan semua data dengan `deposit_product = 'konven'` (termasuk BRI)

#### After Fix:
- Filter "konven" → Hanya menampilkan data dengan SWIFT Code ******** (BNI)
- Filter "merchant" → Hanya menampilkan data dengan SWIFT Code ******** (BRI)
- Filter "syariah" → Menampilkan data dengan `deposit_product = 'syariah'`

### 4. Multiple Tables (Filter "All")
Tetap berfungsi seperti sebelumnya dengan 4 tabel:
1. Deposito Bank Konvensional IDR (SWIFT: ********)
2. Deposito Bank Konvensional Merchant (SWIFT: ********)
3. Deposito Bank Syariah (product type: syariah)
4. Deposito USD (placeholder)

## Testing

### Test Cases Added:
1. **test_specific_product_filters()**: Memvalidasi filter konven dan merchant
2. Memastikan BRI tidak masuk ke filter konven
3. Memastikan BNI tidak masuk ke filter merchant

### Manual Testing:
1. Filter "Konven" → Hanya data BNI (SWIFT: ********)
2. Filter "Merchant" → Hanya data BRI (SWIFT: ********)
3. Filter "Syariah" → Data dengan product type syariah
4. Filter "All" → 4 tabel terpisah

## Files Modified:
- `wizard/time_deposit_report_wizard.py`
- `controllers/time_deposit_report_controller.py`
- `tests/test_time_deposit_report.py` (new)

## Impact:
- ✅ Filter "konven" sekarang hanya menampilkan data BNI
- ✅ Data BRI sekarang memiliki filter terpisah "merchant"
- ✅ Backward compatibility untuk filter "All" dan "syariah"
- ✅ Label yang lebih deskriptif untuk setiap kategori
