# -*- coding: utf-8 -*-

from odoo import api, fields, models, Command
from odoo.exceptions import ValidationError
import logging
from datetime import date, datetime
_logger = logging.getLogger(__name__)


class Partner(models.Model):
    _inherit = 'res.partner'

    vendor_state = fields.Selection(tracking=True)
    hierarchy_id = fields.Many2one('base.hierarchy', string='Approval Hierarchy')
    hierarchy_authorized_id = fields.Many2one('base.hierarchy', string='Hierarchy Authorized')
    approval_history_ids = fields.One2many(
        'res.partner.approval.history', 'partner_id', string='Approval Histories'
    )
    approval_history_detail_ids = fields.One2many('res.partner.approval.detail',
                                           'partner_id', 'Approval History Details')
    approval_history_detail_authorized_ids = fields.One2many('res.partner.authorized.approval.detail',
                                           'partner_authorized_id', 'Approval History Details')
    approval_message_ids = fields.One2many(
        'res.partner.message', 'partner_id', string='Message Histories'
    )
    approval_reassign_ids = fields.One2many(
        'res.partner.reassign', 'partner_id', string='Reassing Employees'
    )
    selected_approver_ids = fields.Many2many(
        'hr.employee',
        string='Selected Approvers',
        compute='_compute_selected_approvers',
        store=True,
    )
    is_current_approver = fields.Boolean(
        string='Current Approver', compute='_compute_selected_approvers'
    )
    is_reassign = fields.Boolean(string='Is Reassign', compute='_compute_reassign')

    def _get_target_state(self):
        self.ensure_one()
        return (
            'prospective'
            if self.partner_type == 'procurement' and self.vendor_state == 'submit'
            else 'authorized'
        )

    def action_supplier_draft(self):
        super().action_supplier_draft()
        self.approval_history_ids = [Command.clear()]
        self.approval_reassign_ids = [Command.clear()]

    def action_prospective(self):
        super().action_prospective()
        self.approval_history_ids = [Command.clear()]
        self.approval_reassign_ids = [Command.clear()]
        self.promote_spend_authorized = False

    def action_submit(self):
        super().action_submit()
        target_state = self._get_target_state()
        if self.partner_type == 'nonprocurement':
            self.write({'vendor_state': 'spendauthorized'})
        else:
            self.action_request_approval(target_state)

    def action_spendauthorized(self):
        super().action_spendauthorized()
        self.action_request_approval(target_state='authorized')

    def _get_active_approvals(self):
        self.ensure_one()
        return self.approval_history_ids.filtered(
            lambda x: x.state not in ['approve', 'reject']
            and x.target_state == self._get_target_state()
        ) if self._get_target_state() == 'prospective' else self.approval_ids.filtered(
            lambda x: x.state not in ['approve', 'reject']
        )

    def get_current_level(self):
        self.ensure_one()
        current_level = self._get_active_approvals().filtered(
            lambda x: x.state == 'in_progress'
        )
        return current_level and current_level[0]

    @api.depends('approval_history_ids.approval_employee_ids', 'approval_ids.approval_employee_ids', 'approval_reassign_ids', 'vendor_state')
    def _compute_selected_approvers(self):
        """compute function to get selected approvers based on hierarchy_id"""
        for partner in self:
            partner.is_current_approver = False
            line = partner.get_current_level()
            if not line:
                continue

            partner.selected_approver_ids = partner.get_employees(line)
            partner.is_current_approver = (
                self.env.user.employee_id in partner.selected_approver_ids
            )

    @api.depends('approval_reassign_ids')
    def _compute_reassign(self):
        for partner in self:
            partner.is_reassign = bool(
                partner.approval_reassign_ids.filtered(
                    lambda x: x.to_employee_id == self.env.user.employee_id
                )
            )

    def action_request_approval(self, target_state=None):
        for partner in self:
            if not target_state:
                target_state = partner._get_approval_type()

            partner._assign_approval(target_state)
            _logger.info(f"{partner._assign_approval(target_state)}wkwkwkwkkw{target_state}")
            # Kirim email ke level approval saat ini
            line = partner.get_current_level()
            employees = partner.get_employees(line)
            emails = employees.filtered('work_email').mapped('work_email') if employees else []

            if emails:
                email_to = ', '.join(emails)
                template = partner._get_approval_template()
                partner.send_email(None, email_to, template)

            # Tambahan: kirim email ke user departemen tertentu jika authorized
            # if target_state == 'authorized':
            #     partner._assign_approval(target_state)
            #     target_departments = ['Procurement Unit', 'Fraud Management Unit', 'Compliance Unit']

            #     departments = self.env['hr.department'].sudo().search([
            #         ('name', 'in', target_departments)
            #     ])

            #     users_target = self.env['hr.employee'].sudo().search([
            #         ('department_id', 'in', departments.ids),
            #         ('user_id', '!=', False),
            #         ('user_id.partner_id.email', '!=', False),
            #     ]).mapped('user_id')

            #     emails_target = users_target.mapped('partner_id.email')

            #     if emails_target:
            #         email_to_target = ', '.join(emails_target)
            #         template = self.env.ref('partner_approval.email_template_authorized_notification')

            #         web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
            #         link = f"{web_url}/odoo/supplier/{partner.id}"
            #         ctx = {'link': link, 'partner_name': partner.name}

            #         partner.send_email(None, email_to_target, 'partner_approval.email_template_authorized_notification')


    def get_employees(self, line=False, employees=False, employee_id=False):
        self.ensure_one()
        if not line:
            return employees

        line.ensure_one()
        if line.approval_by == 'position_department':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))
                domain_emp.append(('job_id', 'in', line.job_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(
                        lambda x: x.to_employee_id.id == employee_id
                    )
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

                employees = self.env['hr.employee'].search(domain_emp)

                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line.approval_by == 'job_level_department':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':           
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))
                domain_emp.append(('job_id.level', '=', line.job_level))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(
                        lambda x: x.to_employee_id.id == employee_id
                    )
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

                employees = self.env['hr.employee'].search(domain_emp)

                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line.approval_by == 'position':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':   
                domain_emp = []
                domain_emp.append(('job_id', 'in', line.job_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(
                        lambda x: x.to_employee_id.id == employee_id
                    )
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    job_exist = line.approval_employee_ids.mapped('job_id')
                    domain_emp.append(('job_id', 'not in', job_exist.ids))

                employees = self.env['hr.employee'].search(domain_emp)

                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line.approval_by == 'department':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting': 
                domain_emp = []
                domain_emp.append(('department_id', 'in', line.department_ids.ids))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(
                        lambda x: x.to_employee_id.id == employee_id
                    )
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                if line.approval_employee_ids:
                    department_exist = line.approval_employee_ids.mapped('department_id')
                    domain_emp.append(('department_id', 'not in', department_exist.ids))

                employees = self.env['hr.employee'].search(domain_emp)

                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        elif line.approval_by == 'employee':
            employees = line.employee_ids

            if employee_id:
                employee = self.env['hr.employee'].browse(employee_id)
                employees = employees - employee

            if employee_id:
                list_emp = []
                list_emp.append(employee_id)
                check_reassign = self.approval_reassign_ids.filtered(
                    lambda x: x.to_employee_id.id == employee_id
                )
                if check_reassign:
                    employee_id2 = check_reassign[0].from_employee_id.id
                    list_emp.append(employee_id2)

                employee = self.env['hr.employee'].browse(list_emp)
                employees = employees - employee

            if line.approval_employee_ids:
                employees = employees - line.approval_employee_ids

            for emp in self.approval_reassign_ids:
                if emp.from_employee_id in employees:
                    employees -= emp.from_employee_id
                    employees += emp.to_employee_id

        elif line.approval_by == 'job_level':
            if line.approval_type == 'paralel' or (line.approval_type == 'serial' and not line.send_unit) or line.approval_type == 'voting':
                domain_emp = []
                domain_emp.append(('job_id.level', '=', line.job_level))

                if employee_id:
                    list_emp = []
                    list_emp.append(employee_id)
                    check_reassign = self.approval_reassign_ids.filtered(
                        lambda x: x.to_employee_id.id == employee_id
                    )
                    if check_reassign:
                        employee_id2 = check_reassign[0].from_employee_id.id
                        list_emp.append(employee_id2)

                    domain_emp.append(('id', 'in', list_emp))

                employees = self.env['hr.employee'].search(domain_emp)

                if line.approval_employee_ids:
                    employees = employees - line.approval_employee_ids

                for emp in self.approval_reassign_ids:
                    if emp.from_employee_id in employees:
                        employees -= emp.from_employee_id
                        employees += emp.to_employee_id

        if line.approval_type == 'serial' and line.approval_employee_ids:
            return self.env['hr.employee']


        return employees

    def _get_approval_template(self):
        """function to return mail template for approval"""
        return 'partner_approval.mail_res_partner_approval'

    def _get_info_template(self):
        """function to return mail template for info"""
        return 'partner_approval.mail_res_partner_info'

    def _assign_approval(self, target_state=None):
        """helper function to assign approval hierarchy to history"""
        self.ensure_one()
        if not self.hierarchy_id:
            hierarchy = (
                self.env['base.hierarchy']
                .sudo()
                .search([('model_id.model', '=', 'res.partner'), ('is_authorized', '=', False)], limit=1)
            )

            if not hierarchy:
                raise ValidationError(
                    'Cannot found hierarchy for Supplier. '
                    'Please check your configuration!'
                )

            self.hierarchy_id = hierarchy.id
        if not self.hierarchy_authorized_id:
            hierarchy_authorized = (
                self.env['base.hierarchy']
                .sudo()
                .search([('model_id.model', '=', 'res.partner'), ('is_authorized', '=', True)], limit=1)
            )

            if not hierarchy_authorized:
                raise ValidationError(
                    'Cannot found authorized hierarchy for Supplier. '
                    'Please check your configuration!'
                )

            self.hierarchy_authorized_id = hierarchy_authorized.id

        if not target_state:
            target_state = self._get_approval_type()

        existing_approvals = self.approval_history_ids.filtered(
            lambda x: x.target_state == target_state
        )
        if existing_approvals:
            existing_approvals += (
                self.approval_history_ids - existing_approvals
            ).filtered(lambda x: x.state != 'approve')
            existing_approvals.unlink()

        self.approval_reassign_ids = [Command.clear()]

        lines = []
        lines_approval_authorized = [(5, 0, 0)]
        lines_detail = []
        lines_detail_authorized = []
        for line in self.hierarchy_id.line_ids.sorted(key=lambda x: x.level):
            vals = {
                'level': line.level,
                'approval_by': line.approval_by,
                'department_ids': line.department_ids,
                'job_ids': line.job_ids,
                'job_level': line.job_level,
                'employee_ids': line.employee_ids,
                'approval_type': line.approval_type,
                'voting_point': line.voting_point,
                'state': 'in_progress' if line.level == 1 else False,
                'target_state': target_state,
            }
            lines.append(Command.create(vals))
        for line in self.hierarchy_authorized_id.line_ids.sorted(key=lambda x: x.level):
            vals = {
                'level': line.level if line.level else False,
                'approval_by': line.approval_by if line.approval_by else False,
                'department_ids': line.department_ids if line.department_ids else False,
                'job_ids': line.job_ids if line.job_ids else False,
                'job_level': line.job_level if line.job_level else False,
                'employee_ids': line.employee_ids if line.employee_ids else False,
                'approval_type': line.approval_type if line.approval_type else False,
                'voting_point': line.voting_point if line.voting_point else False,
                'state': 'in_progress' if line.level == 1 else False,
            }
            lines_approval_authorized.append(Command.create(vals))
        
        if target_state == 'authorized':
            self.approval_ids = lines_approval_authorized
            _logger.info(
                f"Assigning approval hierarchy for {self.name} with {lines_approval_authorized} levels.")
            self.approval_history_detail_authorized_ids = [(5,0,0)]
            for line in self.approval_ids:
                employees = self.get_employees(line)
                for employee in employees:
                    vals_detail = {
                        'level': line.level,
                        'employee_id': employee.id,
                        # 'employee_state': 'approve',
                        # 'employee_date': datetime.now(),
                        # 'employee_note': 'Submit',
                        'reassign_employee_id': False,
                        'reassign_employee_date': False,
                        'reassign_employee_state': False,
                        'reassign_employee_note': False
                    }
                    lines_detail_authorized.append((0, 0, vals_detail))

            self.approval_history_detail_authorized_ids = lines_detail_authorized
        else:
            self.approval_history_ids = lines
        
            self.approval_history_detail_ids = [(5,0,0)]
            for line in self.approval_history_ids:
                employees = self.get_employees(line)
                for employee in employees:
                    vals_detail = {
                        'level': line.level,
                        'employee_id': employee.id,
                        # 'employee_state': 'approve',
                        # 'employee_date': datetime.now(),
                        # 'employee_note': 'Submit',
                        'reassign_employee_id': False,
                        'reassign_employee_date': False,
                        'reassign_employee_state': False,
                        'reassign_employee_note': False
                    }
                    lines_detail.append((0, 0, vals_detail))
            self.approval_history_detail_ids = lines_detail
        

    def action_approval(self):
        ctx = self.env.context
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'res.partner.hierarchy.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_from_employee_id': self.env.user.employee_id.id,
                'default_partner_id': self.id,
                'approve': ctx.get('approve'),
                'reject': ctx.get('reject'),
                'reassign': ctx.get('reassign'),
            },
        }

    def _check_is_complete_approval_line(self, line):
        self.ensure_one()
        if line.approval_type == 'voting':
            is_complete = line.total_voting_point >= line.voting_point
        else:
            is_complete = not bool(self.get_employees(line, True))

        return is_complete

    def _not_complete_approval(self):
        return bool(self._get_active_approvals())

    def _approve(self):
        for partner in self:
            today_date = datetime.now()
            line = partner.get_current_level()

            check_reassign = partner.approval_reassign_ids.filtered(
                lambda x: x.to_employee_id == self.env.user.employee_id
            )
            if check_reassign:
                check_reassign = check_reassign[0]
                line.write({'approval_employee_ids': [(4, check_reassign.from_employee_id.id)]})
                line.total_voting_point += check_reassign.from_employee_id.voting_point

                check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'employee_state': 'approve',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail.write(vals)
                if check_detail_authorized:
                    vals = {
                        'employee_state': 'approve',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail_authorized.write(vals)

                # check details approval reassign
                check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'reassign_employee_state': 'approve',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail.write(vals)
                if check_detail_authorized:
                    vals = {
                        'reassign_employee_state': 'approve',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail_authorized.write(vals)

                check_employee_exist_reassign = partner.get_employees(
                    line, employee_id=self.env.user.employee_id.id
                )
                if check_employee_exist_reassign:
                    line.approval_employee_ids = [
                        Command.link(self.env.user.employee_id.id)
                    ]
                    line.total_voting_point += self.env.user.employee_id.voting_point

            else:
                line.approval_employee_ids = [
                    Command.link(self.env.user.employee_id.id)
                ]
                line.total_voting_point += self.env.user.employee_id.voting_point
                check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'employee_state': 'approve',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail.write(vals)
                if check_detail_authorized:
                    vals = {
                        'employee_state': 'approve',
                        'employee_note': self.env.context.get('note'),
                        'employee_date': today_date
                    }
                    check_detail_authorized.write(vals)
                check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                if check_detail:
                    vals = {
                        'reassign_employee_state': 'approve',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail.write(vals)
                if check_detail_authorized:
                    vals = {
                        'reassign_employee_state': 'approve',
                        'reassign_employee_note': self.env.context.get('note'),
                        'reassign_employee_date': today_date
                    }
                    check_detail_authorized.write(vals)

            message_vals = {
                'employee_id': self.env.user.employee_id.id,
                'date': fields.Datetime.now(),
                'state': 'approve',
                'note': self.env.context.get('note'),
            }
            partner.approval_message_ids = [Command.create(message_vals)]

            level = line.level

            line_check = partner._get_active_approvals().filtered(
                lambda x: x.state == 'in_progress' and x.level == level
            )
            line_check = line_check and line_check[0]

            while line_check:
                check_employee_exist = partner.get_employees(
                    line_check, employee_id=self.env.user.employee_id.id
                )
                if check_employee_exist:
                    check_reassign = partner.approval_reassign_ids.filtered(
                        lambda x: x.to_employee_id == self.env.user.employee_id
                    )
                    if check_reassign:
                        check_reassign = check_reassign[0]
                        line_check.write(
                            {
                                'approval_employee_ids': [
                                    Command.link(check_reassign.from_employee_id.id)
                                ]
                            }
                        )
                        line_check.total_voting_point += (
                            check_reassign.from_employee_id.voting_point
                        )
                        check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                        check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                        if check_detail:
                            vals = {
                                'employee_state': 'approve',
                                'employee_note': self.env.context.get('note'),
                                'employee_date': today_date
                            }
                            check_detail.write(vals)
                        if check_detail_authorized:
                            vals = {
                                'employee_state': 'approve',
                                'employee_note': self.env.context.get('note'),
                                'employee_date': today_date
                            }
                            check_detail_authorized.write(vals)

                        # check details approval reassign
                        check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                        check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                        if check_detail:
                            vals = {
                                'reassign_employee_state': 'approve',
                                'reassign_employee_note': self.env.context.get('note'),
                                'reassign_employee_date': today_date
                            }
                            check_detail.write(vals)
                        if check_detail_authorized:
                            vals = {
                                'reassign_employee_state': 'approve',
                                'reassign_employee_note': self.env.context.get('note'),
                                'reassign_employee_date': today_date
                            }
                            check_detail_authorized.write(vals)

                        check_employee_exist_reassign = partner.get_employees(
                            line_check, employee_id=self.env.user.employee_id.id
                        )
                        if check_employee_exist_reassign:
                            line_check.write(
                                {
                                    'approval_employee_ids': [
                                        Command.link(self.env.user.employee_id.id)
                                    ]
                                }
                            )
                            line_check.total_voting_point += (
                                self.env.user.employee_id.voting_point
                            )

                    else:
                        line_check.write(
                            {
                                'approval_employee_ids': [
                                    Command.link(self.env.user.employee_id.id)
                                ]
                            }
                        )
                        line_check.total_voting_point += (
                            self.env.user.employee_id.voting_point
                        )

                        # check details approval normal
                        check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                        check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
                        if check_detail:
                            vals = {
                                'employee_state': 'approve',
                                'employee_note': self.env.context.get('note'),
                                'employee_date': today_date
                            }
                            check_detail.write(vals)
                        if check_detail_authorized:
                            vals = {
                                'employee_state': 'approve',
                                'employee_note': self.env.context.get('note'),
                                'employee_date': today_date
                            }
                            check_detail_authorized.write(vals)

                        # check details approval reassign
                        check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                        check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
                        if check_detail:
                            vals = {
                                'reassign_employee_state': 'approve',
                                'reassign_employee_note': self.env.context.get('note'),
                                'reassign_employee_date': today_date
                            }
                            check_detail.write(vals)
                        if check_detail_authorized:
                            vals = {
                                'reassign_employee_state': 'approve',
                                'reassign_employee_note': self.env.context.get('note'),
                                'reassign_employee_date': today_date
                            }
                            check_detail_authorized.write(vals)

                before_line = bool(
                    partner._get_active_approvals().filtered(
                        lambda x: x.level < line_check.level
                        and x.state == 'in_progress'
                    )
                )
                if (
                    partner._check_is_complete_approval_line(line_check)
                    and not before_line
                ):
                    line_check.write({'state': 'approve'})

                    next_level = line_check.level + 1
                    next_line = partner._get_active_approvals().filtered(
                        lambda x: x.level == next_level
                    )
                    if next_line:
                        next_line.state = 'in_progress'

                        # send email
                        emails = False
                        employees = partner.get_employees(next_line)
                        if employees:
                            emails = employees.filtered(lambda x: x.work_email).mapped(
                                'work_email'
                            )
                        if emails:
                            email_to = ', '.join(emails)
                            template = partner._get_approval_template()

                            partner.send_email(None, email_to, template)

                # next check
                next_level_check = line_check.level + 1
                line_check = partner._get_active_approvals().filtered(
                    lambda x: x.level == next_level_check
                )

            if not partner._not_complete_approval():
                # main function for approve
                if (
                    partner.partner_type == 'procurement'
                    and partner.vendor_state == 'submit'
                ):
                    #     f"Partner {partner.name} is approved as prospective"
                    # )
                    partner.action_prospective()
                if (
                    partner.partner_type == 'procurement'
                    and partner.vendor_state == 'duediligence' and partner.is_all_detail_filled
                ):
                    partner.approve()

                # send email
                emails = partner.created_by.employee_id.work_email
                if emails:
                    template = partner._get_info_template()
                    message = f"Supplier name {partner.name} has been approved"

                    partner.send_email(message, emails, template)

    def _reject(self):
        for partner in self:
            line = partner.get_current_level()
            line.write({'state': 'reject'})

            today_date = datetime.now()
            # check details approval normal
            check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
            if check_detail:
                vals = {
                    'employee_state': 'reject',
                    'employee_note': self.env.context.get('note'),
                    'employee_date': today_date
                }
                check_detail.write(vals)
            check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.employee_id == self.env.user.employee_id)
            if check_detail_authorized:
                vals = {
                    'employee_state': 'reject',
                    'employee_note': self.env.context.get('note'),
                    'employee_date': today_date
                }
                check_detail_authorized.write(vals)

            # check details approval reassign
            check_detail = partner.approval_history_detail_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
            if check_detail:
                vals = {
                    'reassign_employee_state': 'reject',
                    'reassign_employee_note': self.env.context.get('note'),
                    'reassign_employee_date': today_date
                }
                check_detail.write(vals)
            check_detail_authorized = partner.approval_history_detail_authorized_ids.filtered(lambda x: x.reassign_employee_id == self.env.user.employee_id)
            if check_detail_authorized:
                vals = {
                    'reassign_employee_state': 'reject',
                    'reassign_employee_note': self.env.context.get('note'),
                    'reassign_employee_date': today_date
                }
                check_detail_authorized.write(vals)
            message_vals = {
                'employee_id': self.env.user.employee_id.id,
                'date': fields.Datetime.now(),
                'state': 'reject',
                'note': self.env.context.get('note'),
            }
            partner.approval_message_ids = [Command.create(message_vals)]

            # Set vendor state to the 'draft' because there's not 'reject' state
            partner.action_prospective()

            # send email
            emails = partner.created_by.employee_id.work_email
            if emails:
                email_to = emails
                template = partner._get_info_template()
                message = f"Supplier name {partner.name} has been rejected"

                partner.send_email(message, email_to, template)

    def send_email(self, message, email_to, template):
        """function to send info email to requestor"""
        self.ensure_one()
        if not email_to or not template:
            return False

        template_id = self.sudo().env.ref(template, raise_if_not_found=False)
        if not template_id:
            return False

        web_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
        link = f'{web_url}/odoo/supplier/{self.id}'

        template_id.with_context(
            mail_to=email_to, message=message, link=link
        ).send_mail(self.id, force_send=True)
