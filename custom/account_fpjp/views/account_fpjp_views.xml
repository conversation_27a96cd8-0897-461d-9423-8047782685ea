<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- View account.fpjp View List -->
    <record id="view_account_fpjp_list" model="ir.ui.view">
        <field name="name">view.account.fpjp.list</field>
        <field name="model">account.fpjp</field>
        <field name="arch" type="xml">
            <list decoration-info="state == 'draft'">
                <field name="name"/>
                <field name="description"/>
                <field name="fpjp_type_id" />
                <field name="fpjp_date" />
                <field name="source" />
                <field name="state" widget="badge" decoration-info="state == 'draft'" decoration-success="state == 'approved'" decoration-danger="state == 'canceled'" />

            </list>
        </field>
    </record>

    <!-- View account.fpjp form -->
    <record id="view_account_fpjp_form" model="ir.ui.view">
        <field name="name">view.account.fpjp.form</field>
        <field name="model">account.fpjp</field>
        <field name="arch" type="xml">
            <form string="Account FPJP">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="draft,pending_approval,approved,assign_coa,done"/>
                    <button name="action_set_draft" type="object" string="Set to Draft" invisible="state == 'draft'" class="oe_highlight"/>
                    <button name="action_submit" type="object" string="Submit" invisible="state not in ['draft']" class="oe_highlight"/>
                    <!-- <button name="action_approve" type="object" string="Approve" invisible="state != 'pending_approval'" class="oe_highlight"/> -->
                    <!-- <button name="action_reject" type="object" string="Reject" invisible="state != 'pending_approval'" /> -->
                    <button name="action_assign_coa" type="object" string="Assign CoA" invisible="state != 'approved'" />
                    <button name="action_return" type="object" string="Return" invisible="state != 'pending_approval'" />
                    <button name="action_cancel" type="object" string="Cancel" invisible="state in ['assign_coa', 'cancel', 'done']" />
                    <button name="action_print_fpjp_template" type="object" string="Print FPJP Template" invisible="state != 'done'" />
                    <button name="action_print_verification_sheet" type="object" string="Print FPJP Verification Sheet" invisible="state != 'assign_coa'" />
                    <button name="action_done" type="object" string="Done" invisible="state != 'assign_coa'" class="oe_highlight"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_bill_lines" type="object" class="oe_stat_button" icon="fa-dollar">
                            <field name="count_bills" string="Vendor Bills" widget="statinfo"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="active" invisible="1"/>
                            <field name="description" readonly="state != 'draft'"/>
                            <field name="fpjp_type_id" required='1' readonly="state != 'draft'"/>
                            <field name="tax_verification_type" invisible="1" />
                            <field name="fpjp_date" readonly="1" force_save='1' />
                            <field name="source" readonly="fpjp_type_source != 'manual' or (fpjp_type_source == 'manual' and state != 'draft')" required="not source or fpjp_type_source == 'manual' or state != 'draft'" force_save="1"/>
                            <field name="justification_id" context="{'with_remaining_amount':True, 'hide_pr': True}" domain="[('state','=','approved'),('remaining_amount','>',0)]" invisible="source != 'justification'" required="source == 'justification'" readonly="state != 'draft'" options="{'no_create':True}"/>
                            <field name="supplier_id" domain="[('is_vendor','=', True), ('vendor_state', '=', 'spendauthorized'), ('parent_id','=',False)]" context="{'default_supplier_rank':1}" readonly="state != 'draft'" />
                            <field name="payment_reference" readonly="state != 'draft'"/>
                            <field name="list_id" required='1' readonly="state != 'draft'" options="{'no_create':True,'no_edit':True}" />
                        </group>

                        <group>
                            <field name="employee_id" options="{'no_create':True,'no_edit':True}" readonly="1" force_save="1" />
                            <field name="requestor_id" options="{'no_create':True,'no_edit':True}" readonly="state != 'draft'"/>
                            <field name="unit_id" context="{'hierarchical_naming':False}" domain="[('department_type','=','Unit')]" options="{'no_create':True,'no_edit':True}" readonly="1" force_save="1"/>
                            <field name="group_id" context="{'hierarchical_naming':False}" domain="[('department_type','=','Group')]" options="{'no_create':True,'no_edit':True}" readonly="1" force_save="1"/>
                            <field name="direktorat_id" context="{'hierarchical_naming':False}" domain="[('department_type','=','Directorate')]" options="{'no_create':True,'no_edit':True}" readonly="1" force_save="1"/>
                            <field name="accounting_date" readonly="1"/>
                            <field name="invoice_date" readonly="state != 'draft'"/>
                            <field name="bank_id" context="{'from_fpjp': True}" required='1' domain="[('partner_id','=',supplier_id)]" readonly="state != 'draft'"/>
                            <field name="pay_group" required='1' readonly="state != 'draft'"/>
                            <field name="attachment_ids" required='1' widget="many2many_binary" readonly="state != 'draft'"/>
                        
                        </group>
                        
                    </group>
                    <notebook>
                        <page name="lines" string="FPJP Lines">
                            <field name="line_ids" readonly="state not in ('draft','assign_coa')" force_save="1" nolabel="1">
                                <list editable="bottom">
                                    <field name="flag_asset_active" width="100px" column_invisible="1"/>
                                    <field name="type" width="100px"/>
                                    <field name="name"/>
                                    <!-- <field name="coa_id" column_invisible="parent.state not in ('assign_coa', 'done')" required="parent.state == 'assign_coa'" readonly="parent.state != 'assign_coa'" options="{'no_create':True,'no_edit':True}" domain="[('account_type', 'in', ('liability_non_current', 'liability_current', 'liability_credit_card', 'liability_payable', 'expense', 'expense_depreciation', 'expense_direct_cost'))]" /> -->
                                    <field name="coa_id" column_invisible="parent.state not in ('assign_coa', 'done')" required="parent.state == 'assign_coa'" readonly="parent.state != 'assign_coa'" options="{'no_create':True,'no_edit':True}" />
                                    <field name="category_id" required='1' />
                                    <!-- <field name="justification_line_id" domain="[('justification_id', '=', parent.justification_id)]" options="{'no_create':True,'no_edit':True}" column_invisible="parent.source != 'justification' and not parent.justification_id" optional="hide"/> -->
                                    <field name="justification_line_id" context="{'with_remaining_amount': True}" domain="[('justification_id', '=', parent.justification_id), ('procurement_type_id.type','=','non_procurement')]" column_invisible="parent.source != 'justification'" required="parent.source == 'justification'" options="{'no_create':True,'no_edit':True}" />
                                    <field name="justification_line_desc" readonly='1' force_save='1' column_invisible="1"/>
                                    <field name="justification_id" options="{'no_create':True,'no_edit':True}" column_invisible="1" context="{'list_view_ref': 'account_fpjp.account_justification_fpjp_view_tree'}"/>
                                    <field name="company_currency_id" column_invisible="1" />
                                    <field name="currency_id" options="{'no_create':True,'no_edit':True}" />
                                    <field name="manual_currency_rate_active" force_save="1" readonly="company_currency_id == currency_id" />
                                    <field name="manual_currency_rate_unit" column_invisible="1" />
                                    <field name="manual_currency_rate" readonly="not manual_currency_rate_active" force_save="1" />
                                    <field name="quantity" />
                                    <field name="uom_id" options="{'no_create':True,'no_edit':True}" />
                                    <field name="price_unit" />
                                    <field name="tax_ids" domain="[('type_tax_use','=','purchase')]" options="{'no_create':True,'no_edit':True}" widget="many2many_tags" />
                                    <field name="price_subtotal" />
                                    <field name="price_subtotal_base" />
                                    <field name="amount_total" column_invisible="1"/>
                                    <field name="tax_amt" column_invisible="1"/>
                                    <field name="untaxed_amt" column_invisible="1"/>
                                    <field name="amount_currency" column_invisible="1"/>
                                    <field name="amount_residual" column_invisible="1"/>

                                </list>
                            </field>

                            <group col="12" class="oe_invoice_lines_tab overflow-hidden">
                                <group colspan="8">
                                    <field name="terms_and_condition" placeholder="Terms and Conditions" nolabel="1"/>
                                </group>
                                <!-- Totals (only invoices / receipts) -->
                                <group colspan="4">
                                    <group class="oe_subtotal_footer">
                                        <field name="untaxed_amt" />
                                        <field name="tax_amt" />
                                        <field name="total_amount" />
                                        <!-- <field name="amount_currency" /> -->
                                        <field name="amount_residual" />

                                        

                                    </group>
                                </group>
                            </group>
                            
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- View account.fpjp search -->
    <record id="view_account_fpjp_search" model="ir.ui.view">
        <field name="name">view.account.fpjp.search</field>
        <field name="model">account.fpjp</field>
        <field name="arch" type="xml">
            <search>
                <group expand="1" string="Group By">
                    <filter string="Name" name="name" domain="[]" context="{'group_by':'name'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action account.fpjp -->
    <record id="action_account_fpjp" model="ir.actions.act_window">
        <field name="name">FPJP</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">account.fpjp</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                There is no examples click here to add new Account Fpjp.
            </p>
        </field>
    </record>

</odoo>
