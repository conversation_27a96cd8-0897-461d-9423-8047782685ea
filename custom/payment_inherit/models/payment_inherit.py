# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError


class InvoicingPeriodLine(models.Model):
    _inherit = 'invoicing.period.line'

    move_type = fields.Selection(
        [('out_invoice', "Customer Invoice"), ('in_invoice', "Vendor Bill")],
        string='Type',
        default='out_invoice',
        related='invoice_period_id.move_type',
    )


class AccountPaymentRegister(models.TransientModel):
    _inherit = 'account.payment.register'

    # Add Period
    period_id = fields.Many2one(
        'invoicing.period.line',
        string='Period',
        compute='_compute_get_period',
        store=True,
    )

    @api.depends('payment_date')
    def _compute_get_period(self):
        for rec in self:
            rec.period_id = False
            if rec.payment_date:
                period = (
                    self.env["invoicing.period.line"]
                    .sudo()
                    .search(
                        [
                            ('date_start', '<=', rec.payment_date),
                            ('date_end', '>=', rec.payment_date),
                            ('move_type', '=', 'out_invoice'),
                        ],
                        limit=1,
                    )
                )
                if period:
                    rec.period_id = period.id

    def action_create_payments(self):
        if self.payment_date and self.period_id.state == 'close':
            raise UserError(
                _('You cannot select a payment date from a closed invoice period.')
            )
        return super().action_create_payments()

    def _create_payments(self):
        result = super()._create_payments()
        active_model = self._context.get('active_model', False)
        active_ids = self._context.get('active_ids', [])

        moves = False
        if active_model == 'account.move':
            moves = self.env['account.move'].browse(active_ids)
        elif active_model == 'account.move.line':
            moves = self.env['account.move.line'].browse(active_ids).move_id

        if moves:
            for move in moves:
                payment_invoice = self.env['account.payment.invoice'].create(
                    {
                        'move_id': move.id,
                        'payment_id': result.id,
                    }
                )
                payment_invoice._onchange_set_values()

        return result
