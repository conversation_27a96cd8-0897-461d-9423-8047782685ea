from odoo import api, fields, models, _
from odoo.exceptions import RedirectWarning, UserError, ValidationError, AccessError
from odoo.tools import float_compare, date_utils, email_split, float_is_zero
from odoo.tools.json import json_default
from odoo.tools.misc import formatLang, format_date, get_lang
from json import dumps
import re

import json


class InheritAcountMove(models.Model):
    _inherit = 'account.move'

    def _get_applied(self):
        applied = self.env['applied.invoices']
        for record in self:
            apply = applied.search([('invoice_id', '=', record.id),
                                    ('transaction_type', 'in', ['apply', 'unapply']),
                                    ('state', '=', 'posted')])
            if apply:
                record.applied_misc_ids = [(6, 0, [rec.id for rec in apply])]
            else:
                record.applied_misc_ids = False
    applied_misc_ids = fields.Many2many(
        'applied.invoices', 'invoice_id',
        compute='_get_applied',
        string='Applied from Receipt')
    
    def _post(self, soft=True):
        # OVERRIDE
        posted = super()._post(soft)
        for rec in self:
            if self.env.context.get('from_misc'):
                if ('Time' in rec.name or '-' in rec.name or 'BCT' in rec.name):
                    next_sequence = self.env['ir.sequence'].next_by_code('bank.bank')
                    bank_code = rec.journal_id.code
                    new_sequence = bank_code + next_sequence
                
                    check_move = self.env['account.move'].search([('name', '=', new_sequence)])
                    if check_move:
                        next_sequence = self.env['ir.sequence'].next_by_code('bank.bank')
                        bank_code = rec.journal_id.code
                        new_sequence = bank_code + next_sequence
                        
                    rec.name = new_sequence

        return posted
    
    @api.depends('adjustment_amount') 
    def _compute_amount(self):
        super()._compute_amount()
        for move in self:
            move._compute_payments_widget_reconciled_info()

    @api.depends('move_type', 'line_ids.amount_residual')
    def _compute_payments_widget_reconciled_info(self):
        super(InheritAcountMove, self)._compute_payments_widget_reconciled_info()
        for move in self:
            payments_widget_vals = {'title': _('Less Payment'), 'outstanding': False, 'content': []}
            if move.state == 'posted' and move.is_invoice(include_receipts=True):
                reconciled_vals = []
                reconciled_partials = move.sudo()._get_all_reconciled_invoice_partials()
                for reconciled_partial in reconciled_partials:
                    counterpart_line = reconciled_partial['aml']
                    if counterpart_line.move_id.ref:
                        reconciliation_ref = '%s (%s)' % (counterpart_line.move_id.name, counterpart_line.move_id.ref)
                    else:
                        reconciliation_ref = counterpart_line.move_id.name
                    if counterpart_line.amount_currency and counterpart_line.currency_id != counterpart_line.company_id.currency_id:
                        foreign_currency = counterpart_line.currency_id
                    else:
                        foreign_currency = False

                    reconciled_vals.append({
                        'name': counterpart_line.name if counterpart_line.name else reconciliation_ref,
                        'journal_name': counterpart_line.journal_id.name,
                        'company_name': counterpart_line.journal_id.company_id.name if counterpart_line.journal_id.company_id != move.company_id else False,
                        'amount': reconciled_partial['amount'],
                        'currency_id': move.company_id.currency_id.id if reconciled_partial['is_exchange'] else reconciled_partial['currency'].id,
                        'date': counterpart_line.date,
                        'partial_id': reconciled_partial['partial_id'],
                        'account_payment_id': counterpart_line.payment_id.id,
                        'payment_method_name': counterpart_line.payment_id.payment_method_line_id.name,
                        'move_id': counterpart_line.move_id.id,
                        'ref': reconciliation_ref,
                        # these are necessary for the views to change depending on the values
                        'is_exchange': reconciled_partial['is_exchange'],
                        'amount_company_currency': formatLang(self.env, abs(counterpart_line.balance), currency_obj=counterpart_line.company_id.currency_id),
                        'amount_foreign_currency': foreign_currency and formatLang(self.env, abs(counterpart_line.amount_currency), currency_obj=foreign_currency)
                    })
                payments_widget_vals['content'] = reconciled_vals

            if payments_widget_vals['content']:
                move.invoice_payments_widget = payments_widget_vals
                if move.applied_misc_ids:
                    total_reconcile_amount = total_applied_amount = 0
                    for amount in payments_widget_vals['content']:
                        total_reconcile_amount += amount['amount']

                    is_fully_paid = False

                    for rec in move.applied_misc_ids:
                        if rec.transaction_type == 'apply':
                            if rec.payment_difference_handling in ['full_invoice', 'reconcile']:
                                is_fully_paid = True

                                # total_applied_amount += (rec.applied_amount + rec.payment_difference)
                                # total_reconcile_amount += rec.payment_difference not used
                            else:
                                total_applied_amount += rec.applied_amount 

                            for recon in rec.misc_id.move_id.line_ids:
                                if recon.full_reconcile_id:
                                    for line_move in rec.invoice_id.line_ids:
                                        if not line_move.product_id and line_move.name == line_move.name:
                                            line_move.full_reconcile_id = recon.full_reconcile_id.id
                                            line_move.matching_number = recon.matching_number

                    print('11111', total_applied_amount, total_reconcile_amount)
                    # move.amount_residual_signed = move.amount_total_signed - \
                    #                               (total_applied_amount + total_reconcile_amount)
                    # move.amount_residual = move.amount_total_signed - \
                    #                               (total_applied_amount + total_reconcile_amount)

                    if is_fully_paid:
                        move.amount_residual_signed = 0
                        move.amount_residual = 0
                    else:
                        move.amount_residual_signed = move.amount_total_signed + move.adjustment_amount - total_applied_amount
                        move.amount_residual = move.amount_total_signed + move.adjustment_amount  - total_applied_amount

                    reconciled_amount = 0
                    for applied in move.applied_misc_ids:
                        reconciled_line = applied.misc_id.move_id.line_ids.filtered(lambda x: x.reconciled)
                        for rec_line in reconciled_line:
                            reconciled_amount += rec_line.amount_currency
                    for line in move.line_ids:
                        if line.name == line.move_id.name and not line.product_id and not line.tax_line_id:
                            line.amount_residual = line.move_id.amount_residual
                            line.amount_residual_currency = line.amount_residual
                            if line.amount_residual == 0 and reconciled_amount == line.amount_currency:
                                line.move_id.payment_state = 'paid'
                            elif line.amount_residual == 0 and reconciled_amount != line.amount_currency:
                                line.move_id.payment_state = 'in_payment'
                            elif line.amount_residual > 0:
                                line.move_id.payment_state = 'partial'
                            elif line.amount_residual == line.move_id.amount_total_signed:
                                line.move_id.payment_state = 'not_paid'
            else:
                move.invoice_payments_widget = False
                if move.applied_misc_ids:
                    total_reconcile_amount = total_applied_amount = 0
                    for amount in payments_widget_vals['content']:
                        total_reconcile_amount += amount['amount']

                    is_fully_paid = False

                    for rec in move.applied_misc_ids:
                        if rec.transaction_type == 'apply':
                            if rec.payment_difference_handling in ['full_invoice', 'reconcile']:
                                is_fully_paid = True

                                # total_applied_amount += (rec.applied_amount + rec.payment_difference)
                                # total_reconcile_amount += rec.payment_difference
                            else:
                                total_applied_amount += rec.applied_amount 

                            for recon in rec.misc_id.move_id.line_ids:
                                if recon.full_reconcile_id:
                                    for line_move in rec.invoice_id.line_ids:
                                        if not line_move.product_id \
                                                and not line_move.tax_line_id \
                                                and line_move.name == line_move.move_id.name:
                                            line_move.full_reconcile_id = recon.full_reconcile_id.id
                                            line_move.matching_number = recon.matching_number

                    print('22222', total_applied_amount, total_reconcile_amount)
                    # move.amount_residual_signed = move.amount_total_signed - \
                    #                               total_applied_amount + total_reconcile_amount
                    # move.amount_residual = move.amount_residual_signed
                    if is_fully_paid:
                        move.amount_residual_signed = 0
                        move.amount_residual = 0
                    else:
                        move.amount_residual_signed = move.amount_total_signed + move.adjustment_amount  - total_applied_amount
                        move.amount_residual = move.amount_total_signed + move.adjustment_amount  - total_applied_amount

                    reconciled_amount = 0
                    for applied in move.applied_misc_ids:
                        reconciled_line = applied.misc_id.move_id.line_ids.filtered(lambda x: x.reconciled)
                        for rec_line in reconciled_line:
                            reconciled_amount += rec_line.amount_currency
                    for line in move.line_ids:
                        if line.name == line.move_id.name and not line.product_id and not line.tax_line_id:
                            line.amount_residual = line.move_id.amount_residual
                            line.amount_residual_currency = line.amount_residual
                            if line.amount_residual == 0 and reconciled_amount == line.amount_currency:
                                line.move_id.payment_state = 'paid'
                            elif line.amount_residual == 0 and reconciled_amount != line.amount_currency:
                                line.move_id.payment_state = 'in_payment'
                            elif line.amount_residual > 0:
                                line.move_id.payment_state = 'partial'
                            elif line.amount_residual == line.move_id.amount_total_signed:
                                line.move_id.payment_state = 'not_paid'


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'


    @api.constrains('matching_number', 'matched_debit_ids', 'matched_credit_ids')
    def _constrains_matching_number(self):
        for line in self:
            pass
            # if line.matching_number:
            #     if not re.match(r'^((P?\d+)|(I.+))$', line.matching_number):
            #         raise Exception("Invalid matching number format")
            #     elif line.matching_number.startswith('I') and (line.matched_debit_ids or line.matched_credit_ids):
            #         raise ValidationError(_("A temporary number can not be used in a real matching"))
            #     elif line.matching_number.startswith('P') and not (line.matched_debit_ids or line.matched_credit_ids):
            #         raise Exception("Should have partials")
            #     elif line.matching_number.startswith('P') and line.full_reconcile_id:
            #         raise Exception("Should not be partial number")
            #     elif line.matching_number.isdecimal() and not line.full_reconcile_id:
            #         raise Exception("Should not be full number")
            #     elif line.full_reconcile_id and line.matching_number != str(line.full_reconcile_id.id):
            #         raise Exception("Matching number should be the full reconcile") 
            # elif line.matched_debit_ids or line.matched_credit_ids:
            #     raise Exception("Should have number")